confection-0.1.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
confection-0.1.5.dist-info/LICENSE,sha256=9_dOqC-aENfOK6Bd1-RgyMK7QqbMfZrnGrNWYbX3HJs,1073
confection-0.1.5.dist-info/METADATA,sha256=E4IAhPzCjN22pZCJlF2sfzw02BfE3kchnaZhv-H_ulM,19597
confection-0.1.5.dist-info/RECORD,,
confection-0.1.5.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
confection-0.1.5.dist-info/top_level.txt,sha256=IW0MglPluqVnP26bD2x7YDauBNEgafciyjlKd7s2dzM,11
confection-0.1.5.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
confection/__init__.py,sha256=wuhtsxT6aFk0XEou9P-MmxInKUS5a7eEghCIyvbXYHs,47656
confection/__pycache__/__init__.cpython-312.pyc,,
confection/__pycache__/util.cpython-312.pyc,,
confection/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
confection/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
confection/tests/__pycache__/__init__.cpython-312.pyc,,
confection/tests/__pycache__/conftest.cpython-312.pyc,,
confection/tests/__pycache__/test_config.cpython-312.pyc,,
confection/tests/__pycache__/test_frozen_structures.cpython-312.pyc,,
confection/tests/__pycache__/util.cpython-312.pyc,,
confection/tests/conftest.py,sha256=06QImBbGxE7pAv3x8JMehJN9xvGD8Y9n71MhYQIMN2Y,489
confection/tests/test_config.py,sha256=pknGSLihotM7iLi0KYeCfOTx1uHWxaXcKWCyjxzLY_I,54505
confection/tests/test_frozen_structures.py,sha256=OJyqk-xY4hu8yDrAbX8cSpvA9FlLpl49GISh1cmQ2cY,1713
confection/tests/util.py,sha256=CSUh9wOm5OMAdjbDP4vDT0GOdHGT8RgYCq8CUhx1SLk,3686
confection/util.py,sha256=0iohHQdDgFd8ec57mhj41zA7wihKvTPcXKLv7JoKLbo,4300
