{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\upload.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './upload.css'; // Make sure to create this CSS file\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadPDF = () => {\n  _s();\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [uploadStatus, setUploadStatus] = useState(\"\");\n  const handleFileChange = event => {\n    const newFiles = Array.from(event.target.files);\n    const uniqueNewFiles = newFiles.filter(file => !selectedFiles.some(f => f.name === file.name));\n    setSelectedFiles(prev => [...prev, ...uniqueNewFiles]);\n    setUploadStatus(\"\");\n  };\n  const handleUpload = async () => {\n    if (selectedFiles.length === 0) {\n      setUploadStatus(\"⚠️ Please select PDF files first.\");\n      return;\n    }\n    const formData = new FormData();\n    selectedFiles.forEach(file => {\n      formData.append(\"pdf_file\", file);\n    });\n    try {\n      const token = localStorage.getItem(\"access\");\n      const response = await axios.post('http://localhost:8000/api/upload_pdf/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          ...(token && {\n            Authorization: `Bearer ${token}`\n          })\n        }\n      });\n      if (response.status === 200) {\n        setUploadStatus(\"✅ All files uploaded successfully.\");\n        setSelectedFiles([]);\n      } else {\n        setUploadStatus(\"⚠️ Upload failed. Try again.\");\n      }\n    } catch (error) {\n      console.error(\"Upload error:\", error);\n      setUploadStatus(\"❌ Error uploading files.\");\n    }\n  };\n  const handleRemoveFile = index => {\n    const updatedFiles = [...selectedFiles];\n    updatedFiles.splice(index, 1);\n    setSelectedFiles(updatedFiles);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"upload-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83D\\uDCC4 PDF Upload Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"upload-label\",\n      children: \"Select PDF Files:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      accept: \".pdf\",\n      multiple: true,\n      onChange: handleFileChange,\n      className: \"upload-input\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"file-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDDC2\\uFE0F Selected Files:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: selectedFiles.map((file, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [file.name, /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRemoveFile(index),\n            className: \"remove-btn\",\n            children: \"\\u274C Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleUpload,\n      className: \"upload-button\",\n      disabled: selectedFiles.length === 0,\n      children: \"\\uD83D\\uDE80 Upload Files\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), uploadStatus && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"upload-status\",\n      children: uploadStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 24\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadPDF, \"SBWg3zta8/1NkjS9LIYOdsyTEJg=\");\n_c = UploadPDF;\nexport default UploadPDF;\nvar _c;\n$RefreshReg$(_c, \"UploadPDF\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "UploadPDF", "_s", "selectedFiles", "setSelectedFiles", "uploadStatus", "setUploadStatus", "handleFileChange", "event", "newFiles", "Array", "from", "target", "files", "uniqueNewFiles", "filter", "file", "some", "f", "name", "prev", "handleUpload", "length", "formData", "FormData", "for<PERSON>ach", "append", "token", "localStorage", "getItem", "response", "post", "headers", "Authorization", "status", "error", "console", "handleRemoveFile", "index", "updatedFiles", "splice", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "accept", "multiple", "onChange", "map", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/upload.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport './upload.css'; // Make sure to create this CSS file\r\n\r\nconst UploadPDF = () => {\r\n  const [selectedFiles, setSelectedFiles] = useState([]);\r\n  const [uploadStatus, setUploadStatus] = useState(\"\");\r\n\r\n  const handleFileChange = (event) => {\r\n    const newFiles = Array.from(event.target.files);\r\n    const uniqueNewFiles = newFiles.filter(file =>\r\n      !selectedFiles.some(f => f.name === file.name)\r\n    );\r\n    setSelectedFiles(prev => [...prev, ...uniqueNewFiles]);\r\n    setUploadStatus(\"\");\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    if (selectedFiles.length === 0) {\r\n      setUploadStatus(\"⚠️ Please select PDF files first.\");\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    selectedFiles.forEach(file => {\r\n      formData.append(\"pdf_file\", file);\r\n    });\r\n\r\n    try {\r\n      const token = localStorage.getItem(\"access\");\r\n      const response = await axios.post('http://localhost:8000/api/upload_pdf/', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          ...(token && { Authorization: `Bearer ${token}` }),\r\n        }\r\n      });\r\n\r\n      if (response.status === 200) {\r\n        setUploadStatus(\"✅ All files uploaded successfully.\");\r\n        setSelectedFiles([]);\r\n      } else {\r\n        setUploadStatus(\"⚠️ Upload failed. Try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Upload error:\", error);\r\n      setUploadStatus(\"❌ Error uploading files.\");\r\n    }\r\n  };\r\n\r\n  const handleRemoveFile = (index) => {\r\n    const updatedFiles = [...selectedFiles];\r\n    updatedFiles.splice(index, 1);\r\n    setSelectedFiles(updatedFiles);\r\n  };\r\n\r\n  return (\r\n    <div className=\"upload-container\">\r\n      <h1>📄 PDF Upload Manager</h1>\r\n\r\n      <label className=\"upload-label\">Select PDF Files:</label>\r\n      <input type=\"file\" accept=\".pdf\" multiple onChange={handleFileChange} className=\"upload-input\" />\r\n\r\n      {selectedFiles.length > 0 && (\r\n        <div className=\"file-list\">\r\n          <h4>🗂️ Selected Files:</h4>\r\n          <ul>\r\n            {selectedFiles.map((file, index) => (\r\n              <li key={index}>\r\n                {file.name}\r\n                <button onClick={() => handleRemoveFile(index)} className=\"remove-btn\">❌ Remove</button>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n\r\n      <button onClick={handleUpload} className=\"upload-button\" disabled={selectedFiles.length === 0}>\r\n        🚀 Upload Files\r\n      </button>\r\n\r\n      {uploadStatus && <p className=\"upload-status\">{uploadStatus}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadPDF;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMU,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC;IAC/C,MAAMC,cAAc,GAAGL,QAAQ,CAACM,MAAM,CAACC,IAAI,IACzC,CAACb,aAAa,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI,CAC/C,CAAC;IACDf,gBAAgB,CAACgB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGN,cAAc,CAAC,CAAC;IACtDR,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIlB,aAAa,CAACmB,MAAM,KAAK,CAAC,EAAE;MAC9BhB,eAAe,CAAC,mCAAmC,CAAC;MACpD;IACF;IAEA,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BrB,aAAa,CAACsB,OAAO,CAACT,IAAI,IAAI;MAC5BO,QAAQ,CAACG,MAAM,CAAC,UAAU,EAAEV,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI;MACF,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC5C,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,IAAI,CAAC,uCAAuC,EAAER,QAAQ,EAAE;QACnFS,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,IAAIL,KAAK,IAAI;YAAEM,aAAa,EAAE,UAAUN,KAAK;UAAG,CAAC;QACnD;MACF,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;QAC3B5B,eAAe,CAAC,oCAAoC,CAAC;QACrDF,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAC,MAAM;QACLE,eAAe,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC7B,eAAe,CAAC,0BAA0B,CAAC;IAC7C;EACF,CAAC;EAED,MAAM+B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,YAAY,GAAG,CAAC,GAAGpC,aAAa,CAAC;IACvCoC,YAAY,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC7BlC,gBAAgB,CAACmC,YAAY,CAAC;EAChC,CAAC;EAED,oBACEvC,OAAA;IAAKyC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B1C,OAAA;MAAA0C,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE9B9C,OAAA;MAAOyC,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACzD9C,OAAA;MAAO+C,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,MAAM;MAACC,QAAQ;MAACC,QAAQ,EAAE3C,gBAAiB;MAACkC,SAAS,EAAC;IAAc;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEhG3C,aAAa,CAACmB,MAAM,GAAG,CAAC,iBACvBtB,OAAA;MAAKyC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB1C,OAAA;QAAA0C,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B9C,OAAA;QAAA0C,QAAA,EACGvC,aAAa,CAACgD,GAAG,CAAC,CAACnC,IAAI,EAAEsB,KAAK,kBAC7BtC,OAAA;UAAA0C,QAAA,GACG1B,IAAI,CAACG,IAAI,eACVnB,OAAA;YAAQoD,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACC,KAAK,CAAE;YAACG,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAFjFR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAED9C,OAAA;MAAQoD,OAAO,EAAE/B,YAAa;MAACoB,SAAS,EAAC,eAAe;MAACY,QAAQ,EAAElD,aAAa,CAACmB,MAAM,KAAK,CAAE;MAAAoB,QAAA,EAAC;IAE/F;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAERzC,YAAY,iBAAIL,OAAA;MAAGyC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAErC;IAAY;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7D,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA/EID,SAAS;AAAAqD,EAAA,GAATrD,SAAS;AAiFf,eAAeA,SAAS;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}