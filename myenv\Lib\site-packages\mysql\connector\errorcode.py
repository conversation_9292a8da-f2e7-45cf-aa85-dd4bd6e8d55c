# -*- coding: utf-8 -*-

# Copyright (c) 2013, 2024, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation. The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

"""This module contains the MySQL Server and Client error codes."""

# This file was auto-generated.
_GENERATED_ON = "2021-08-11"
_MYSQL_VERSION = (8, 0, 27)

# Start MySQL Errors
OBSOLETE_ER_HASHCHK = 1000
OBSOLETE_ER_NISAMCHK = 1001
ER_NO = 1002
ER_YES = 1003
ER_CANT_CREATE_FILE = 1004
ER_CANT_CREATE_TABLE = 1005
ER_CANT_CREATE_DB = 1006
ER_DB_CREATE_EXISTS = 1007
ER_DB_DROP_EXISTS = 1008
OBSOLETE_ER_DB_DROP_DELETE = 1009
ER_DB_DROP_RMDIR = 1010
OBSOLETE_ER_CANT_DELETE_FILE = 1011
ER_CANT_FIND_SYSTEM_REC = 1012
ER_CANT_GET_STAT = 1013
OBSOLETE_ER_CANT_GET_WD = 1014
ER_CANT_LOCK = 1015
ER_CANT_OPEN_FILE = 1016
ER_FILE_NOT_FOUND = 1017
ER_CANT_READ_DIR = 1018
OBSOLETE_ER_CANT_SET_WD = 1019
ER_CHECKREAD = 1020
OBSOLETE_ER_DISK_FULL = 1021
ER_DUP_KEY = 1022
OBSOLETE_ER_ERROR_ON_CLOSE = 1023
ER_ERROR_ON_READ = 1024
ER_ERROR_ON_RENAME = 1025
ER_ERROR_ON_WRITE = 1026
ER_FILE_USED = 1027
OBSOLETE_ER_FILSORT_ABORT = 1028
OBSOLETE_ER_FORM_NOT_FOUND = 1029
ER_GET_ERRNO = 1030
ER_ILLEGAL_HA = 1031
ER_KEY_NOT_FOUND = 1032
ER_NOT_FORM_FILE = 1033
ER_NOT_KEYFILE = 1034
ER_OLD_KEYFILE = 1035
ER_OPEN_AS_READONLY = 1036
ER_OUTOFMEMORY = 1037
ER_OUT_OF_SORTMEMORY = 1038
OBSOLETE_ER_UNEXPECTED_EOF = 1039
ER_CON_COUNT_ERROR = 1040
ER_OUT_OF_RESOURCES = 1041
ER_BAD_HOST_ERROR = 1042
ER_HANDSHAKE_ERROR = 1043
ER_DBACCESS_DENIED_ERROR = 1044
ER_ACCESS_DENIED_ERROR = 1045
ER_NO_DB_ERROR = 1046
ER_UNKNOWN_COM_ERROR = 1047
ER_BAD_NULL_ERROR = 1048
ER_BAD_DB_ERROR = 1049
ER_TABLE_EXISTS_ERROR = 1050
ER_BAD_TABLE_ERROR = 1051
ER_NON_UNIQ_ERROR = 1052
ER_SERVER_SHUTDOWN = 1053
ER_BAD_FIELD_ERROR = 1054
ER_WRONG_FIELD_WITH_GROUP = 1055
ER_WRONG_GROUP_FIELD = 1056
ER_WRONG_SUM_SELECT = 1057
ER_WRONG_VALUE_COUNT = 1058
ER_TOO_LONG_IDENT = 1059
ER_DUP_FIELDNAME = 1060
ER_DUP_KEYNAME = 1061
ER_DUP_ENTRY = 1062
ER_WRONG_FIELD_SPEC = 1063
ER_PARSE_ERROR = 1064
ER_EMPTY_QUERY = 1065
ER_NONUNIQ_TABLE = 1066
ER_INVALID_DEFAULT = 1067
ER_MULTIPLE_PRI_KEY = 1068
ER_TOO_MANY_KEYS = 1069
ER_TOO_MANY_KEY_PARTS = 1070
ER_TOO_LONG_KEY = 1071
ER_KEY_COLUMN_DOES_NOT_EXITS = 1072
ER_BLOB_USED_AS_KEY = 1073
ER_TOO_BIG_FIELDLENGTH = 1074
ER_WRONG_AUTO_KEY = 1075
ER_READY = 1076
OBSOLETE_ER_NORMAL_SHUTDOWN = 1077
OBSOLETE_ER_GOT_SIGNAL = 1078
ER_SHUTDOWN_COMPLETE = 1079
ER_FORCING_CLOSE = 1080
ER_IPSOCK_ERROR = 1081
ER_NO_SUCH_INDEX = 1082
ER_WRONG_FIELD_TERMINATORS = 1083
ER_BLOBS_AND_NO_TERMINATED = 1084
ER_TEXTFILE_NOT_READABLE = 1085
ER_FILE_EXISTS_ERROR = 1086
ER_LOAD_INFO = 1087
ER_ALTER_INFO = 1088
ER_WRONG_SUB_KEY = 1089
ER_CANT_REMOVE_ALL_FIELDS = 1090
ER_CANT_DROP_FIELD_OR_KEY = 1091
ER_INSERT_INFO = 1092
ER_UPDATE_TABLE_USED = 1093
ER_NO_SUCH_THREAD = 1094
ER_KILL_DENIED_ERROR = 1095
ER_NO_TABLES_USED = 1096
ER_TOO_BIG_SET = 1097
ER_NO_UNIQUE_LOGFILE = 1098
ER_TABLE_NOT_LOCKED_FOR_WRITE = 1099
ER_TABLE_NOT_LOCKED = 1100
ER_BLOB_CANT_HAVE_DEFAULT = 1101
ER_WRONG_DB_NAME = 1102
ER_WRONG_TABLE_NAME = 1103
ER_TOO_BIG_SELECT = 1104
ER_UNKNOWN_ERROR = 1105
ER_UNKNOWN_PROCEDURE = 1106
ER_WRONG_PARAMCOUNT_TO_PROCEDURE = 1107
ER_WRONG_PARAMETERS_TO_PROCEDURE = 1108
ER_UNKNOWN_TABLE = 1109
ER_FIELD_SPECIFIED_TWICE = 1110
ER_INVALID_GROUP_FUNC_USE = 1111
ER_UNSUPPORTED_EXTENSION = 1112
ER_TABLE_MUST_HAVE_COLUMNS = 1113
ER_RECORD_FILE_FULL = 1114
ER_UNKNOWN_CHARACTER_SET = 1115
ER_TOO_MANY_TABLES = 1116
ER_TOO_MANY_FIELDS = 1117
ER_TOO_BIG_ROWSIZE = 1118
ER_STACK_OVERRUN = 1119
ER_WRONG_OUTER_JOIN_UNUSED = 1120
ER_NULL_COLUMN_IN_INDEX = 1121
ER_CANT_FIND_UDF = 1122
ER_CANT_INITIALIZE_UDF = 1123
ER_UDF_NO_PATHS = 1124
ER_UDF_EXISTS = 1125
ER_CANT_OPEN_LIBRARY = 1126
ER_CANT_FIND_DL_ENTRY = 1127
ER_FUNCTION_NOT_DEFINED = 1128
ER_HOST_IS_BLOCKED = 1129
ER_HOST_NOT_PRIVILEGED = 1130
ER_PASSWORD_ANONYMOUS_USER = 1131
ER_PASSWORD_NOT_ALLOWED = 1132
ER_PASSWORD_NO_MATCH = 1133
ER_UPDATE_INFO = 1134
ER_CANT_CREATE_THREAD = 1135
ER_WRONG_VALUE_COUNT_ON_ROW = 1136
ER_CANT_REOPEN_TABLE = 1137
ER_INVALID_USE_OF_NULL = 1138
ER_REGEXP_ERROR = 1139
ER_MIX_OF_GROUP_FUNC_AND_FIELDS = 1140
ER_NONEXISTING_GRANT = 1141
ER_TABLEACCESS_DENIED_ERROR = 1142
ER_COLUMNACCESS_DENIED_ERROR = 1143
ER_ILLEGAL_GRANT_FOR_TABLE = 1144
ER_GRANT_WRONG_HOST_OR_USER = 1145
ER_NO_SUCH_TABLE = 1146
ER_NONEXISTING_TABLE_GRANT = 1147
ER_NOT_ALLOWED_COMMAND = 1148
ER_SYNTAX_ERROR = 1149
OBSOLETE_ER_UNUSED1 = 1150
OBSOLETE_ER_UNUSED2 = 1151
ER_ABORTING_CONNECTION = 1152
ER_NET_PACKET_TOO_LARGE = 1153
ER_NET_READ_ERROR_FROM_PIPE = 1154
ER_NET_FCNTL_ERROR = 1155
ER_NET_PACKETS_OUT_OF_ORDER = 1156
ER_NET_UNCOMPRESS_ERROR = 1157
ER_NET_READ_ERROR = 1158
ER_NET_READ_INTERRUPTED = 1159
ER_NET_ERROR_ON_WRITE = 1160
ER_NET_WRITE_INTERRUPTED = 1161
ER_TOO_LONG_STRING = 1162
ER_TABLE_CANT_HANDLE_BLOB = 1163
ER_TABLE_CANT_HANDLE_AUTO_INCREMENT = 1164
OBSOLETE_ER_UNUSED3 = 1165
ER_WRONG_COLUMN_NAME = 1166
ER_WRONG_KEY_COLUMN = 1167
ER_WRONG_MRG_TABLE = 1168
ER_DUP_UNIQUE = 1169
ER_BLOB_KEY_WITHOUT_LENGTH = 1170
ER_PRIMARY_CANT_HAVE_NULL = 1171
ER_TOO_MANY_ROWS = 1172
ER_REQUIRES_PRIMARY_KEY = 1173
OBSOLETE_ER_NO_RAID_COMPILED = 1174
ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE = 1175
ER_KEY_DOES_NOT_EXITS = 1176
ER_CHECK_NO_SUCH_TABLE = 1177
ER_CHECK_NOT_IMPLEMENTED = 1178
ER_CANT_DO_THIS_DURING_AN_TRANSACTION = 1179
ER_ERROR_DURING_COMMIT = 1180
ER_ERROR_DURING_ROLLBACK = 1181
ER_ERROR_DURING_FLUSH_LOGS = 1182
OBSOLETE_ER_ERROR_DURING_CHECKPOINT = 1183
ER_NEW_ABORTING_CONNECTION = 1184
OBSOLETE_ER_DUMP_NOT_IMPLEMENTED = 1185
OBSOLETE_ER_FLUSH_MASTER_BINLOG_CLOSED = 1186
OBSOLETE_ER_INDEX_REBUILD = 1187
ER_MASTER = 1188
ER_MASTER_NET_READ = 1189
ER_MASTER_NET_WRITE = 1190
ER_FT_MATCHING_KEY_NOT_FOUND = 1191
ER_LOCK_OR_ACTIVE_TRANSACTION = 1192
ER_UNKNOWN_SYSTEM_VARIABLE = 1193
ER_CRASHED_ON_USAGE = 1194
ER_CRASHED_ON_REPAIR = 1195
ER_WARNING_NOT_COMPLETE_ROLLBACK = 1196
ER_TRANS_CACHE_FULL = 1197
OBSOLETE_ER_SLAVE_MUST_STOP = 1198
ER_SLAVE_NOT_RUNNING = 1199
ER_BAD_SLAVE = 1200
ER_MASTER_INFO = 1201
ER_SLAVE_THREAD = 1202
ER_TOO_MANY_USER_CONNECTIONS = 1203
ER_SET_CONSTANTS_ONLY = 1204
ER_LOCK_WAIT_TIMEOUT = 1205
ER_LOCK_TABLE_FULL = 1206
ER_READ_ONLY_TRANSACTION = 1207
OBSOLETE_ER_DROP_DB_WITH_READ_LOCK = 1208
OBSOLETE_ER_CREATE_DB_WITH_READ_LOCK = 1209
ER_WRONG_ARGUMENTS = 1210
ER_NO_PERMISSION_TO_CREATE_USER = 1211
OBSOLETE_ER_UNION_TABLES_IN_DIFFERENT_DIR = 1212
ER_LOCK_DEADLOCK = 1213
ER_TABLE_CANT_HANDLE_FT = 1214
ER_CANNOT_ADD_FOREIGN = 1215
ER_NO_REFERENCED_ROW = 1216
ER_ROW_IS_REFERENCED = 1217
ER_CONNECT_TO_MASTER = 1218
OBSOLETE_ER_QUERY_ON_MASTER = 1219
ER_ERROR_WHEN_EXECUTING_COMMAND = 1220
ER_WRONG_USAGE = 1221
ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT = 1222
ER_CANT_UPDATE_WITH_READLOCK = 1223
ER_MIXING_NOT_ALLOWED = 1224
ER_DUP_ARGUMENT = 1225
ER_USER_LIMIT_REACHED = 1226
ER_SPECIFIC_ACCESS_DENIED_ERROR = 1227
ER_LOCAL_VARIABLE = 1228
ER_GLOBAL_VARIABLE = 1229
ER_NO_DEFAULT = 1230
ER_WRONG_VALUE_FOR_VAR = 1231
ER_WRONG_TYPE_FOR_VAR = 1232
ER_VAR_CANT_BE_READ = 1233
ER_CANT_USE_OPTION_HERE = 1234
ER_NOT_SUPPORTED_YET = 1235
ER_MASTER_FATAL_ERROR_READING_BINLOG = 1236
ER_SLAVE_IGNORED_TABLE = 1237
ER_INCORRECT_GLOBAL_LOCAL_VAR = 1238
ER_WRONG_FK_DEF = 1239
ER_KEY_REF_DO_NOT_MATCH_TABLE_REF = 1240
ER_OPERAND_COLUMNS = 1241
ER_SUBQUERY_NO_1_ROW = 1242
ER_UNKNOWN_STMT_HANDLER = 1243
ER_CORRUPT_HELP_DB = 1244
OBSOLETE_ER_CYCLIC_REFERENCE = 1245
ER_AUTO_CONVERT = 1246
ER_ILLEGAL_REFERENCE = 1247
ER_DERIVED_MUST_HAVE_ALIAS = 1248
ER_SELECT_REDUCED = 1249
ER_TABLENAME_NOT_ALLOWED_HERE = 1250
ER_NOT_SUPPORTED_AUTH_MODE = 1251
ER_SPATIAL_CANT_HAVE_NULL = 1252
ER_COLLATION_CHARSET_MISMATCH = 1253
OBSOLETE_ER_SLAVE_WAS_RUNNING = 1254
OBSOLETE_ER_SLAVE_WAS_NOT_RUNNING = 1255
ER_TOO_BIG_FOR_UNCOMPRESS = 1256
ER_ZLIB_Z_MEM_ERROR = 1257
ER_ZLIB_Z_BUF_ERROR = 1258
ER_ZLIB_Z_DATA_ERROR = 1259
ER_CUT_VALUE_GROUP_CONCAT = 1260
ER_WARN_TOO_FEW_RECORDS = 1261
ER_WARN_TOO_MANY_RECORDS = 1262
ER_WARN_NULL_TO_NOTNULL = 1263
ER_WARN_DATA_OUT_OF_RANGE = 1264
WARN_DATA_TRUNCATED = 1265
ER_WARN_USING_OTHER_HANDLER = 1266
ER_CANT_AGGREGATE_2COLLATIONS = 1267
OBSOLETE_ER_DROP_USER = 1268
ER_REVOKE_GRANTS = 1269
ER_CANT_AGGREGATE_3COLLATIONS = 1270
ER_CANT_AGGREGATE_NCOLLATIONS = 1271
ER_VARIABLE_IS_NOT_STRUCT = 1272
ER_UNKNOWN_COLLATION = 1273
ER_SLAVE_IGNORED_SSL_PARAMS = 1274
OBSOLETE_ER_SERVER_IS_IN_SECURE_AUTH_MODE = 1275
ER_WARN_FIELD_RESOLVED = 1276
ER_BAD_SLAVE_UNTIL_COND = 1277
ER_MISSING_SKIP_SLAVE = 1278
ER_UNTIL_COND_IGNORED = 1279
ER_WRONG_NAME_FOR_INDEX = 1280
ER_WRONG_NAME_FOR_CATALOG = 1281
OBSOLETE_ER_WARN_QC_RESIZE = 1282
ER_BAD_FT_COLUMN = 1283
ER_UNKNOWN_KEY_CACHE = 1284
ER_WARN_HOSTNAME_WONT_WORK = 1285
ER_UNKNOWN_STORAGE_ENGINE = 1286
ER_WARN_DEPRECATED_SYNTAX = 1287
ER_NON_UPDATABLE_TABLE = 1288
ER_FEATURE_DISABLED = 1289
ER_OPTION_PREVENTS_STATEMENT = 1290
ER_DUPLICATED_VALUE_IN_TYPE = 1291
ER_TRUNCATED_WRONG_VALUE = 1292
OBSOLETE_ER_TOO_MUCH_AUTO_TIMESTAMP_COLS = 1293
ER_INVALID_ON_UPDATE = 1294
ER_UNSUPPORTED_PS = 1295
ER_GET_ERRMSG = 1296
ER_GET_TEMPORARY_ERRMSG = 1297
ER_UNKNOWN_TIME_ZONE = 1298
ER_WARN_INVALID_TIMESTAMP = 1299
ER_INVALID_CHARACTER_STRING = 1300
ER_WARN_ALLOWED_PACKET_OVERFLOWED = 1301
ER_CONFLICTING_DECLARATIONS = 1302
ER_SP_NO_RECURSIVE_CREATE = 1303
ER_SP_ALREADY_EXISTS = 1304
ER_SP_DOES_NOT_EXIST = 1305
ER_SP_DROP_FAILED = 1306
ER_SP_STORE_FAILED = 1307
ER_SP_LILABEL_MISMATCH = 1308
ER_SP_LABEL_REDEFINE = 1309
ER_SP_LABEL_MISMATCH = 1310
ER_SP_UNINIT_VAR = 1311
ER_SP_BADSELECT = 1312
ER_SP_BADRETURN = 1313
ER_SP_BADSTATEMENT = 1314
ER_UPDATE_LOG_DEPRECATED_IGNORED = 1315
ER_UPDATE_LOG_DEPRECATED_TRANSLATED = 1316
ER_QUERY_INTERRUPTED = 1317
ER_SP_WRONG_NO_OF_ARGS = 1318
ER_SP_COND_MISMATCH = 1319
ER_SP_NORETURN = 1320
ER_SP_NORETURNEND = 1321
ER_SP_BAD_CURSOR_QUERY = 1322
ER_SP_BAD_CURSOR_SELECT = 1323
ER_SP_CURSOR_MISMATCH = 1324
ER_SP_CURSOR_ALREADY_OPEN = 1325
ER_SP_CURSOR_NOT_OPEN = 1326
ER_SP_UNDECLARED_VAR = 1327
ER_SP_WRONG_NO_OF_FETCH_ARGS = 1328
ER_SP_FETCH_NO_DATA = 1329
ER_SP_DUP_PARAM = 1330
ER_SP_DUP_VAR = 1331
ER_SP_DUP_COND = 1332
ER_SP_DUP_CURS = 1333
ER_SP_CANT_ALTER = 1334
ER_SP_SUBSELECT_NYI = 1335
ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG = 1336
ER_SP_VARCOND_AFTER_CURSHNDLR = 1337
ER_SP_CURSOR_AFTER_HANDLER = 1338
ER_SP_CASE_NOT_FOUND = 1339
ER_FPARSER_TOO_BIG_FILE = 1340
ER_FPARSER_BAD_HEADER = 1341
ER_FPARSER_EOF_IN_COMMENT = 1342
ER_FPARSER_ERROR_IN_PARAMETER = 1343
ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER = 1344
ER_VIEW_NO_EXPLAIN = 1345
OBSOLETE_ER_FRM_UNKNOWN_TYPE = 1346
ER_WRONG_OBJECT = 1347
ER_NONUPDATEABLE_COLUMN = 1348
OBSOLETE_ER_VIEW_SELECT_DERIVED_UNUSED = 1349
ER_VIEW_SELECT_CLAUSE = 1350
ER_VIEW_SELECT_VARIABLE = 1351
ER_VIEW_SELECT_TMPTABLE = 1352
ER_VIEW_WRONG_LIST = 1353
ER_WARN_VIEW_MERGE = 1354
ER_WARN_VIEW_WITHOUT_KEY = 1355
ER_VIEW_INVALID = 1356
ER_SP_NO_DROP_SP = 1357
OBSOLETE_ER_SP_GOTO_IN_HNDLR = 1358
ER_TRG_ALREADY_EXISTS = 1359
ER_TRG_DOES_NOT_EXIST = 1360
ER_TRG_ON_VIEW_OR_TEMP_TABLE = 1361
ER_TRG_CANT_CHANGE_ROW = 1362
ER_TRG_NO_SUCH_ROW_IN_TRG = 1363
ER_NO_DEFAULT_FOR_FIELD = 1364
ER_DIVISION_BY_ZERO = 1365
ER_TRUNCATED_WRONG_VALUE_FOR_FIELD = 1366
ER_ILLEGAL_VALUE_FOR_TYPE = 1367
ER_VIEW_NONUPD_CHECK = 1368
ER_VIEW_CHECK_FAILED = 1369
ER_PROCACCESS_DENIED_ERROR = 1370
ER_RELAY_LOG_FAIL = 1371
OBSOLETE_ER_PASSWD_LENGTH = 1372
ER_UNKNOWN_TARGET_BINLOG = 1373
ER_IO_ERR_LOG_INDEX_READ = 1374
ER_BINLOG_PURGE_PROHIBITED = 1375
ER_FSEEK_FAIL = 1376
ER_BINLOG_PURGE_FATAL_ERR = 1377
ER_LOG_IN_USE = 1378
ER_LOG_PURGE_UNKNOWN_ERR = 1379
ER_RELAY_LOG_INIT = 1380
ER_NO_BINARY_LOGGING = 1381
ER_RESERVED_SYNTAX = 1382
OBSOLETE_ER_WSAS_FAILED = 1383
OBSOLETE_ER_DIFF_GROUPS_PROC = 1384
OBSOLETE_ER_NO_GROUP_FOR_PROC = 1385
OBSOLETE_ER_ORDER_WITH_PROC = 1386
OBSOLETE_ER_LOGGING_PROHIBIT_CHANGING_OF = 1387
OBSOLETE_ER_NO_FILE_MAPPING = 1388
OBSOLETE_ER_WRONG_MAGIC = 1389
ER_PS_MANY_PARAM = 1390
ER_KEY_PART_0 = 1391
ER_VIEW_CHECKSUM = 1392
ER_VIEW_MULTIUPDATE = 1393
ER_VIEW_NO_INSERT_FIELD_LIST = 1394
ER_VIEW_DELETE_MERGE_VIEW = 1395
ER_CANNOT_USER = 1396
ER_XAER_NOTA = 1397
ER_XAER_INVAL = 1398
ER_XAER_RMFAIL = 1399
ER_XAER_OUTSIDE = 1400
ER_XAER_RMERR = 1401
ER_XA_RBROLLBACK = 1402
ER_NONEXISTING_PROC_GRANT = 1403
ER_PROC_AUTO_GRANT_FAIL = 1404
ER_PROC_AUTO_REVOKE_FAIL = 1405
ER_DATA_TOO_LONG = 1406
ER_SP_BAD_SQLSTATE = 1407
ER_STARTUP = 1408
ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR = 1409
ER_CANT_CREATE_USER_WITH_GRANT = 1410
ER_WRONG_VALUE_FOR_TYPE = 1411
ER_TABLE_DEF_CHANGED = 1412
ER_SP_DUP_HANDLER = 1413
ER_SP_NOT_VAR_ARG = 1414
ER_SP_NO_RETSET = 1415
ER_CANT_CREATE_GEOMETRY_OBJECT = 1416
OBSOLETE_ER_FAILED_ROUTINE_BREAK_BINLOG = 1417
ER_BINLOG_UNSAFE_ROUTINE = 1418
ER_BINLOG_CREATE_ROUTINE_NEED_SUPER = 1419
OBSOLETE_ER_EXEC_STMT_WITH_OPEN_CURSOR = 1420
ER_STMT_HAS_NO_OPEN_CURSOR = 1421
ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG = 1422
ER_NO_DEFAULT_FOR_VIEW_FIELD = 1423
ER_SP_NO_RECURSION = 1424
ER_TOO_BIG_SCALE = 1425
ER_TOO_BIG_PRECISION = 1426
ER_M_BIGGER_THAN_D = 1427
ER_WRONG_LOCK_OF_SYSTEM_TABLE = 1428
ER_CONNECT_TO_FOREIGN_DATA_SOURCE = 1429
ER_QUERY_ON_FOREIGN_DATA_SOURCE = 1430
ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST = 1431
ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE = 1432
ER_FOREIGN_DATA_STRING_INVALID = 1433
OBSOLETE_ER_CANT_CREATE_FEDERATED_TABLE = 1434
ER_TRG_IN_WRONG_SCHEMA = 1435
ER_STACK_OVERRUN_NEED_MORE = 1436
ER_TOO_LONG_BODY = 1437
ER_WARN_CANT_DROP_DEFAULT_KEYCACHE = 1438
ER_TOO_BIG_DISPLAYWIDTH = 1439
ER_XAER_DUPID = 1440
ER_DATETIME_FUNCTION_OVERFLOW = 1441
ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG = 1442
ER_VIEW_PREVENT_UPDATE = 1443
ER_PS_NO_RECURSION = 1444
ER_SP_CANT_SET_AUTOCOMMIT = 1445
OBSOLETE_ER_MALFORMED_DEFINER = 1446
ER_VIEW_FRM_NO_USER = 1447
ER_VIEW_OTHER_USER = 1448
ER_NO_SUCH_USER = 1449
ER_FORBID_SCHEMA_CHANGE = 1450
ER_ROW_IS_REFERENCED_2 = 1451
ER_NO_REFERENCED_ROW_2 = 1452
ER_SP_BAD_VAR_SHADOW = 1453
ER_TRG_NO_DEFINER = 1454
ER_OLD_FILE_FORMAT = 1455
ER_SP_RECURSION_LIMIT = 1456
OBSOLETE_ER_SP_PROC_TABLE_CORRUPT = 1457
ER_SP_WRONG_NAME = 1458
ER_TABLE_NEEDS_UPGRADE = 1459
ER_SP_NO_AGGREGATE = 1460
ER_MAX_PREPARED_STMT_COUNT_REACHED = 1461
ER_VIEW_RECURSIVE = 1462
ER_NON_GROUPING_FIELD_USED = 1463
ER_TABLE_CANT_HANDLE_SPKEYS = 1464
ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA = 1465
ER_REMOVED_SPACES = 1466
ER_AUTOINC_READ_FAILED = 1467
ER_USERNAME = 1468
ER_HOSTNAME = 1469
ER_WRONG_STRING_LENGTH = 1470
ER_NON_INSERTABLE_TABLE = 1471
ER_ADMIN_WRONG_MRG_TABLE = 1472
ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT = 1473
ER_NAME_BECOMES_EMPTY = 1474
ER_AMBIGUOUS_FIELD_TERM = 1475
ER_FOREIGN_SERVER_EXISTS = 1476
ER_FOREIGN_SERVER_DOESNT_EXIST = 1477
ER_ILLEGAL_HA_CREATE_OPTION = 1478
ER_PARTITION_REQUIRES_VALUES_ERROR = 1479
ER_PARTITION_WRONG_VALUES_ERROR = 1480
ER_PARTITION_MAXVALUE_ERROR = 1481
OBSOLETE_ER_PARTITION_SUBPARTITION_ERROR = 1482
OBSOLETE_ER_PARTITION_SUBPART_MIX_ERROR = 1483
ER_PARTITION_WRONG_NO_PART_ERROR = 1484
ER_PARTITION_WRONG_NO_SUBPART_ERROR = 1485
ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR = 1486
OBSOLETE_ER_NO_CONST_EXPR_IN_RANGE_OR_LIST_ERROR = 1487
ER_FIELD_NOT_FOUND_PART_ERROR = 1488
OBSOLETE_ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR = 1489
ER_INCONSISTENT_PARTITION_INFO_ERROR = 1490
ER_PARTITION_FUNC_NOT_ALLOWED_ERROR = 1491
ER_PARTITIONS_MUST_BE_DEFINED_ERROR = 1492
ER_RANGE_NOT_INCREASING_ERROR = 1493
ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR = 1494
ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR = 1495
ER_PARTITION_ENTRY_ERROR = 1496
ER_MIX_HANDLER_ERROR = 1497
ER_PARTITION_NOT_DEFINED_ERROR = 1498
ER_TOO_MANY_PARTITIONS_ERROR = 1499
ER_SUBPARTITION_ERROR = 1500
ER_CANT_CREATE_HANDLER_FILE = 1501
ER_BLOB_FIELD_IN_PART_FUNC_ERROR = 1502
ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF = 1503
ER_NO_PARTS_ERROR = 1504
ER_PARTITION_MGMT_ON_NONPARTITIONED = 1505
ER_FOREIGN_KEY_ON_PARTITIONED = 1506
ER_DROP_PARTITION_NON_EXISTENT = 1507
ER_DROP_LAST_PARTITION = 1508
ER_COALESCE_ONLY_ON_HASH_PARTITION = 1509
ER_REORG_HASH_ONLY_ON_SAME_NO = 1510
ER_REORG_NO_PARAM_ERROR = 1511
ER_ONLY_ON_RANGE_LIST_PARTITION = 1512
ER_ADD_PARTITION_SUBPART_ERROR = 1513
ER_ADD_PARTITION_NO_NEW_PARTITION = 1514
ER_COALESCE_PARTITION_NO_PARTITION = 1515
ER_REORG_PARTITION_NOT_EXIST = 1516
ER_SAME_NAME_PARTITION = 1517
ER_NO_BINLOG_ERROR = 1518
ER_CONSECUTIVE_REORG_PARTITIONS = 1519
ER_REORG_OUTSIDE_RANGE = 1520
ER_PARTITION_FUNCTION_FAILURE = 1521
OBSOLETE_ER_PART_STATE_ERROR = 1522
ER_LIMITED_PART_RANGE = 1523
ER_PLUGIN_IS_NOT_LOADED = 1524
ER_WRONG_VALUE = 1525
ER_NO_PARTITION_FOR_GIVEN_VALUE = 1526
ER_FILEGROUP_OPTION_ONLY_ONCE = 1527
ER_CREATE_FILEGROUP_FAILED = 1528
ER_DROP_FILEGROUP_FAILED = 1529
ER_TABLESPACE_AUTO_EXTEND_ERROR = 1530
ER_WRONG_SIZE_NUMBER = 1531
ER_SIZE_OVERFLOW_ERROR = 1532
ER_ALTER_FILEGROUP_FAILED = 1533
ER_BINLOG_ROW_LOGGING_FAILED = 1534
OBSOLETE_ER_BINLOG_ROW_WRONG_TABLE_DEF = 1535
OBSOLETE_ER_BINLOG_ROW_RBR_TO_SBR = 1536
ER_EVENT_ALREADY_EXISTS = 1537
OBSOLETE_ER_EVENT_STORE_FAILED = 1538
ER_EVENT_DOES_NOT_EXIST = 1539
OBSOLETE_ER_EVENT_CANT_ALTER = 1540
OBSOLETE_ER_EVENT_DROP_FAILED = 1541
ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG = 1542
ER_EVENT_ENDS_BEFORE_STARTS = 1543
ER_EVENT_EXEC_TIME_IN_THE_PAST = 1544
OBSOLETE_ER_EVENT_OPEN_TABLE_FAILED = 1545
OBSOLETE_ER_EVENT_NEITHER_M_EXPR_NOR_M_AT = 1546
OBSOLETE_ER_COL_COUNT_DOESNT_MATCH_CORRUPTED = 1547
OBSOLETE_ER_CANNOT_LOAD_FROM_TABLE = 1548
OBSOLETE_ER_EVENT_CANNOT_DELETE = 1549
OBSOLETE_ER_EVENT_COMPILE_ERROR = 1550
ER_EVENT_SAME_NAME = 1551
OBSOLETE_ER_EVENT_DATA_TOO_LONG = 1552
ER_DROP_INDEX_FK = 1553
ER_WARN_DEPRECATED_SYNTAX_WITH_VER = 1554
OBSOLETE_ER_CANT_WRITE_LOCK_LOG_TABLE = 1555
ER_CANT_LOCK_LOG_TABLE = 1556
ER_FOREIGN_DUPLICATE_KEY_OLD_UNUSED = 1557
ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE = 1558
OBSOLETE_ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR = 1559
ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT = 1560
OBSOLETE_ER_NDB_CANT_SWITCH_BINLOG_FORMAT = 1561
ER_PARTITION_NO_TEMPORARY = 1562
ER_PARTITION_CONST_DOMAIN_ERROR = 1563
ER_PARTITION_FUNCTION_IS_NOT_ALLOWED = 1564
OBSOLETE_ER_DDL_LOG_ERROR_UNUSED = 1565
ER_NULL_IN_VALUES_LESS_THAN = 1566
ER_WRONG_PARTITION_NAME = 1567
ER_CANT_CHANGE_TX_CHARACTERISTICS = 1568
ER_DUP_ENTRY_AUTOINCREMENT_CASE = 1569
OBSOLETE_ER_EVENT_MODIFY_QUEUE_ERROR = 1570
ER_EVENT_SET_VAR_ERROR = 1571
ER_PARTITION_MERGE_ERROR = 1572
OBSOLETE_ER_CANT_ACTIVATE_LOG = 1573
OBSOLETE_ER_RBR_NOT_AVAILABLE = 1574
ER_BASE64_DECODE_ERROR = 1575
ER_EVENT_RECURSION_FORBIDDEN = 1576
OBSOLETE_ER_EVENTS_DB_ERROR = 1577
ER_ONLY_INTEGERS_ALLOWED = 1578
ER_UNSUPORTED_LOG_ENGINE = 1579
ER_BAD_LOG_STATEMENT = 1580
ER_CANT_RENAME_LOG_TABLE = 1581
ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT = 1582
ER_WRONG_PARAMETERS_TO_NATIVE_FCT = 1583
ER_WRONG_PARAMETERS_TO_STORED_FCT = 1584
ER_NATIVE_FCT_NAME_COLLISION = 1585
ER_DUP_ENTRY_WITH_KEY_NAME = 1586
ER_BINLOG_PURGE_EMFILE = 1587
ER_EVENT_CANNOT_CREATE_IN_THE_PAST = 1588
ER_EVENT_CANNOT_ALTER_IN_THE_PAST = 1589
OBSOLETE_ER_SLAVE_INCIDENT = 1590
ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT = 1591
ER_BINLOG_UNSAFE_STATEMENT = 1592
ER_BINLOG_FATAL_ERROR = 1593
OBSOLETE_ER_SLAVE_RELAY_LOG_READ_FAILURE = 1594
OBSOLETE_ER_SLAVE_RELAY_LOG_WRITE_FAILURE = 1595
OBSOLETE_ER_SLAVE_CREATE_EVENT_FAILURE = 1596
OBSOLETE_ER_SLAVE_MASTER_COM_FAILURE = 1597
ER_BINLOG_LOGGING_IMPOSSIBLE = 1598
ER_VIEW_NO_CREATION_CTX = 1599
ER_VIEW_INVALID_CREATION_CTX = 1600
OBSOLETE_ER_SR_INVALID_CREATION_CTX = 1601
ER_TRG_CORRUPTED_FILE = 1602
ER_TRG_NO_CREATION_CTX = 1603
ER_TRG_INVALID_CREATION_CTX = 1604
ER_EVENT_INVALID_CREATION_CTX = 1605
ER_TRG_CANT_OPEN_TABLE = 1606
OBSOLETE_ER_CANT_CREATE_SROUTINE = 1607
OBSOLETE_ER_NEVER_USED = 1608
ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT = 1609
ER_SLAVE_CORRUPT_EVENT = 1610
OBSOLETE_ER_LOAD_DATA_INVALID_COLUMN_UNUSED = 1611
ER_LOG_PURGE_NO_FILE = 1612
ER_XA_RBTIMEOUT = 1613
ER_XA_RBDEADLOCK = 1614
ER_NEED_REPREPARE = 1615
OBSOLETE_ER_DELAYED_NOT_SUPPORTED = 1616
WARN_NO_MASTER_INFO = 1617
WARN_OPTION_IGNORED = 1618
ER_PLUGIN_DELETE_BUILTIN = 1619
WARN_PLUGIN_BUSY = 1620
ER_VARIABLE_IS_READONLY = 1621
ER_WARN_ENGINE_TRANSACTION_ROLLBACK = 1622
OBSOLETE_ER_SLAVE_HEARTBEAT_FAILURE = 1623
ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE = 1624
ER_NDB_REPLICATION_SCHEMA_ERROR = 1625
ER_CONFLICT_FN_PARSE_ERROR = 1626
ER_EXCEPTIONS_WRITE_ERROR = 1627
ER_TOO_LONG_TABLE_COMMENT = 1628
ER_TOO_LONG_FIELD_COMMENT = 1629
ER_FUNC_INEXISTENT_NAME_COLLISION = 1630
ER_DATABASE_NAME = 1631
ER_TABLE_NAME = 1632
ER_PARTITION_NAME = 1633
ER_SUBPARTITION_NAME = 1634
ER_TEMPORARY_NAME = 1635
ER_RENAMED_NAME = 1636
ER_TOO_MANY_CONCURRENT_TRXS = 1637
WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED = 1638
ER_DEBUG_SYNC_TIMEOUT = 1639
ER_DEBUG_SYNC_HIT_LIMIT = 1640
ER_DUP_SIGNAL_SET = 1641
ER_SIGNAL_WARN = 1642
ER_SIGNAL_NOT_FOUND = 1643
ER_SIGNAL_EXCEPTION = 1644
ER_RESIGNAL_WITHOUT_ACTIVE_HANDLER = 1645
ER_SIGNAL_BAD_CONDITION_TYPE = 1646
WARN_COND_ITEM_TRUNCATED = 1647
ER_COND_ITEM_TOO_LONG = 1648
ER_UNKNOWN_LOCALE = 1649
ER_SLAVE_IGNORE_SERVER_IDS = 1650
OBSOLETE_ER_QUERY_CACHE_DISABLED = 1651
ER_SAME_NAME_PARTITION_FIELD = 1652
ER_PARTITION_COLUMN_LIST_ERROR = 1653
ER_WRONG_TYPE_COLUMN_VALUE_ERROR = 1654
ER_TOO_MANY_PARTITION_FUNC_FIELDS_ERROR = 1655
ER_MAXVALUE_IN_VALUES_IN = 1656
ER_TOO_MANY_VALUES_ERROR = 1657
ER_ROW_SINGLE_PARTITION_FIELD_ERROR = 1658
ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD = 1659
ER_PARTITION_FIELDS_TOO_LONG = 1660
ER_BINLOG_ROW_ENGINE_AND_STMT_ENGINE = 1661
ER_BINLOG_ROW_MODE_AND_STMT_ENGINE = 1662
ER_BINLOG_UNSAFE_AND_STMT_ENGINE = 1663
ER_BINLOG_ROW_INJECTION_AND_STMT_ENGINE = 1664
ER_BINLOG_STMT_MODE_AND_ROW_ENGINE = 1665
ER_BINLOG_ROW_INJECTION_AND_STMT_MODE = 1666
ER_BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE = 1667
ER_BINLOG_UNSAFE_LIMIT = 1668
OBSOLETE_ER_UNUSED4 = 1669
ER_BINLOG_UNSAFE_SYSTEM_TABLE = 1670
ER_BINLOG_UNSAFE_AUTOINC_COLUMNS = 1671
ER_BINLOG_UNSAFE_UDF = 1672
ER_BINLOG_UNSAFE_SYSTEM_VARIABLE = 1673
ER_BINLOG_UNSAFE_SYSTEM_FUNCTION = 1674
ER_BINLOG_UNSAFE_NONTRANS_AFTER_TRANS = 1675
ER_MESSAGE_AND_STATEMENT = 1676
OBSOLETE_ER_SLAVE_CONVERSION_FAILED = 1677
ER_SLAVE_CANT_CREATE_CONVERSION = 1678
ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT = 1679
ER_PATH_LENGTH = 1680
ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT = 1681
ER_WRONG_NATIVE_TABLE_STRUCTURE = 1682
ER_WRONG_PERFSCHEMA_USAGE = 1683
ER_WARN_I_S_SKIPPED_TABLE = 1684
ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT = 1685
ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT = 1686
ER_SPATIAL_MUST_HAVE_GEOM_COL = 1687
ER_TOO_LONG_INDEX_COMMENT = 1688
ER_LOCK_ABORTED = 1689
ER_DATA_OUT_OF_RANGE = 1690
OBSOLETE_ER_WRONG_SPVAR_TYPE_IN_LIMIT = 1691
ER_BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE = 1692
ER_BINLOG_UNSAFE_MIXED_STATEMENT = 1693
ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN = 1694
ER_STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN = 1695
ER_FAILED_READ_FROM_PAR_FILE = 1696
ER_VALUES_IS_NOT_INT_TYPE_ERROR = 1697
ER_ACCESS_DENIED_NO_PASSWORD_ERROR = 1698
ER_SET_PASSWORD_AUTH_PLUGIN = 1699
OBSOLETE_ER_GRANT_PLUGIN_USER_EXISTS = 1700
ER_TRUNCATE_ILLEGAL_FK = 1701
ER_PLUGIN_IS_PERMANENT = 1702
ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN = 1703
ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX = 1704
ER_STMT_CACHE_FULL = 1705
ER_MULTI_UPDATE_KEY_CONFLICT = 1706
ER_TABLE_NEEDS_REBUILD = 1707
WARN_OPTION_BELOW_LIMIT = 1708
ER_INDEX_COLUMN_TOO_LONG = 1709
ER_ERROR_IN_TRIGGER_BODY = 1710
ER_ERROR_IN_UNKNOWN_TRIGGER_BODY = 1711
ER_INDEX_CORRUPT = 1712
ER_UNDO_RECORD_TOO_BIG = 1713
ER_BINLOG_UNSAFE_INSERT_IGNORE_SELECT = 1714
ER_BINLOG_UNSAFE_INSERT_SELECT_UPDATE = 1715
ER_BINLOG_UNSAFE_REPLACE_SELECT = 1716
ER_BINLOG_UNSAFE_CREATE_IGNORE_SELECT = 1717
ER_BINLOG_UNSAFE_CREATE_REPLACE_SELECT = 1718
ER_BINLOG_UNSAFE_UPDATE_IGNORE = 1719
ER_PLUGIN_NO_UNINSTALL = 1720
ER_PLUGIN_NO_INSTALL = 1721
ER_BINLOG_UNSAFE_WRITE_AUTOINC_SELECT = 1722
ER_BINLOG_UNSAFE_CREATE_SELECT_AUTOINC = 1723
ER_BINLOG_UNSAFE_INSERT_TWO_KEYS = 1724
ER_TABLE_IN_FK_CHECK = 1725
ER_UNSUPPORTED_ENGINE = 1726
ER_BINLOG_UNSAFE_AUTOINC_NOT_FIRST = 1727
ER_CANNOT_LOAD_FROM_TABLE_V2 = 1728
ER_MASTER_DELAY_VALUE_OUT_OF_RANGE = 1729
ER_ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT = 1730
ER_PARTITION_EXCHANGE_DIFFERENT_OPTION = 1731
ER_PARTITION_EXCHANGE_PART_TABLE = 1732
ER_PARTITION_EXCHANGE_TEMP_TABLE = 1733
ER_PARTITION_INSTEAD_OF_SUBPARTITION = 1734
ER_UNKNOWN_PARTITION = 1735
ER_TABLES_DIFFERENT_METADATA = 1736
ER_ROW_DOES_NOT_MATCH_PARTITION = 1737
ER_BINLOG_CACHE_SIZE_GREATER_THAN_MAX = 1738
ER_WARN_INDEX_NOT_APPLICABLE = 1739
ER_PARTITION_EXCHANGE_FOREIGN_KEY = 1740
OBSOLETE_ER_NO_SUCH_KEY_VALUE = 1741
ER_RPL_INFO_DATA_TOO_LONG = 1742
OBSOLETE_ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE = 1743
OBSOLETE_ER_BINLOG_READ_EVENT_CHECKSUM_FAILURE = 1744
ER_BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX = 1745
ER_CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT = 1746
ER_PARTITION_CLAUSE_ON_NONPARTITIONED = 1747
ER_ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET = 1748
OBSOLETE_ER_NO_SUCH_PARTITION__UNUSED = 1749
ER_CHANGE_RPL_INFO_REPOSITORY_FAILURE = 1750
ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE = 1751
ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE = 1752
ER_MTS_FEATURE_IS_NOT_SUPPORTED = 1753
ER_MTS_UPDATED_DBS_GREATER_MAX = 1754
ER_MTS_CANT_PARALLEL = 1755
ER_MTS_INCONSISTENT_DATA = 1756
ER_FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING = 1757
ER_DA_INVALID_CONDITION_NUMBER = 1758
ER_INSECURE_PLAIN_TEXT = 1759
ER_INSECURE_CHANGE_MASTER = 1760
ER_FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO = 1761
ER_FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO = 1762
ER_SQLTHREAD_WITH_SECURE_SLAVE = 1763
ER_TABLE_HAS_NO_FT = 1764
ER_VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER = 1765
ER_VARIABLE_NOT_SETTABLE_IN_TRANSACTION = 1766
OBSOLETE_ER_GTID_NEXT_IS_NOT_IN_GTID_NEXT_LIST = 1767
OBSOLETE_ER_CANT_CHANGE_GTID_NEXT_IN_TRANSACTION = 1768
ER_SET_STATEMENT_CANNOT_INVOKE_FUNCTION = 1769
ER_GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL = 1770
OBSOLETE_ER_SKIPPING_LOGGED_TRANSACTION = 1771
ER_MALFORMED_GTID_SET_SPECIFICATION = 1772
ER_MALFORMED_GTID_SET_ENCODING = 1773
ER_MALFORMED_GTID_SPECIFICATION = 1774
ER_GNO_EXHAUSTED = 1775
ER_BAD_SLAVE_AUTO_POSITION = 1776
ER_AUTO_POSITION_REQUIRES_GTID_MODE_NOT_OFF = 1777
ER_CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET = 1778
ER_GTID_MODE_ON_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON = 1779
OBSOLETE_ER_GTID_MODE_REQUIRES_BINLOG = 1780
ER_CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF = 1781
ER_CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON = 1782
ER_CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF = 1783
OBSOLETE_ER_FOUND_GTID_EVENT_WHEN_GTID_MODE_IS_OFF__UNUSED = 1784
ER_GTID_UNSAFE_NON_TRANSACTIONAL_TABLE = 1785
ER_GTID_UNSAFE_CREATE_SELECT = 1786
OBSOLETE_ER_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRANSACTION = 1787
ER_GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME = 1788
ER_MASTER_HAS_PURGED_REQUIRED_GTIDS = 1789
ER_CANT_SET_GTID_NEXT_WHEN_OWNING_GTID = 1790
ER_UNKNOWN_EXPLAIN_FORMAT = 1791
ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION = 1792
ER_TOO_LONG_TABLE_PARTITION_COMMENT = 1793
ER_SLAVE_CONFIGURATION = 1794
ER_INNODB_FT_LIMIT = 1795
ER_INNODB_NO_FT_TEMP_TABLE = 1796
ER_INNODB_FT_WRONG_DOCID_COLUMN = 1797
ER_INNODB_FT_WRONG_DOCID_INDEX = 1798
ER_INNODB_ONLINE_LOG_TOO_BIG = 1799
ER_UNKNOWN_ALTER_ALGORITHM = 1800
ER_UNKNOWN_ALTER_LOCK = 1801
ER_MTS_CHANGE_MASTER_CANT_RUN_WITH_GAPS = 1802
ER_MTS_RECOVERY_FAILURE = 1803
ER_MTS_RESET_WORKERS = 1804
ER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2 = 1805
ER_SLAVE_SILENT_RETRY_TRANSACTION = 1806
ER_DISCARD_FK_CHECKS_RUNNING = 1807
ER_TABLE_SCHEMA_MISMATCH = 1808
ER_TABLE_IN_SYSTEM_TABLESPACE = 1809
ER_IO_READ_ERROR = 1810
ER_IO_WRITE_ERROR = 1811
ER_TABLESPACE_MISSING = 1812
ER_TABLESPACE_EXISTS = 1813
ER_TABLESPACE_DISCARDED = 1814
ER_INTERNAL_ERROR = 1815
ER_INNODB_IMPORT_ERROR = 1816
ER_INNODB_INDEX_CORRUPT = 1817
ER_INVALID_YEAR_COLUMN_LENGTH = 1818
ER_NOT_VALID_PASSWORD = 1819
ER_MUST_CHANGE_PASSWORD = 1820
ER_FK_NO_INDEX_CHILD = 1821
ER_FK_NO_INDEX_PARENT = 1822
ER_FK_FAIL_ADD_SYSTEM = 1823
ER_FK_CANNOT_OPEN_PARENT = 1824
ER_FK_INCORRECT_OPTION = 1825
ER_FK_DUP_NAME = 1826
ER_PASSWORD_FORMAT = 1827
ER_FK_COLUMN_CANNOT_DROP = 1828
ER_FK_COLUMN_CANNOT_DROP_CHILD = 1829
ER_FK_COLUMN_NOT_NULL = 1830
ER_DUP_INDEX = 1831
ER_FK_COLUMN_CANNOT_CHANGE = 1832
ER_FK_COLUMN_CANNOT_CHANGE_CHILD = 1833
OBSOLETE_ER_UNUSED5 = 1834
ER_MALFORMED_PACKET = 1835
ER_READ_ONLY_MODE = 1836
ER_GTID_NEXT_TYPE_UNDEFINED_GTID = 1837
ER_VARIABLE_NOT_SETTABLE_IN_SP = 1838
OBSOLETE_ER_CANT_SET_GTID_PURGED_WHEN_GTID_MODE_IS_OFF = 1839
ER_CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY = 1840
ER_CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY = 1841
ER_GTID_PURGED_WAS_CHANGED = 1842
ER_GTID_EXECUTED_WAS_CHANGED = 1843
ER_BINLOG_STMT_MODE_AND_NO_REPL_TABLES = 1844
ER_ALTER_OPERATION_NOT_SUPPORTED = 1845
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON = 1846
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY = 1847
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION = 1848
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME = 1849
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE = 1850
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK = 1851
OBSOLETE_ER_UNUSED6 = 1852
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK = 1853
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC = 1854
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS = 1855
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS = 1856
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS = 1857
OBSOLETE_ER_SQL_REPLICA_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE = 1858
ER_DUP_UNKNOWN_IN_INDEX = 1859
ER_IDENT_CAUSES_TOO_LONG_PATH = 1860
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL = 1861
ER_MUST_CHANGE_PASSWORD_LOGIN = 1862
ER_ROW_IN_WRONG_PARTITION = 1863
ER_MTS_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX = 1864
OBSOLETE_ER_INNODB_NO_FT_USES_PARSER = 1865
ER_BINLOG_LOGICAL_CORRUPTION = 1866
ER_WARN_PURGE_LOG_IN_USE = 1867
ER_WARN_PURGE_LOG_IS_ACTIVE = 1868
ER_AUTO_INCREMENT_CONFLICT = 1869
WARN_ON_BLOCKHOLE_IN_RBR = 1870
ER_SLAVE_MI_INIT_REPOSITORY = 1871
ER_SLAVE_RLI_INIT_REPOSITORY = 1872
ER_ACCESS_DENIED_CHANGE_USER_ERROR = 1873
ER_INNODB_READ_ONLY = 1874
ER_STOP_SLAVE_SQL_THREAD_TIMEOUT = 1875
ER_STOP_SLAVE_IO_THREAD_TIMEOUT = 1876
ER_TABLE_CORRUPT = 1877
ER_TEMP_FILE_WRITE_FAILURE = 1878
ER_INNODB_FT_AUX_NOT_HEX_ID = 1879
ER_OLD_TEMPORALS_UPGRADED = 1880
ER_INNODB_FORCED_RECOVERY = 1881
ER_AES_INVALID_IV = 1882
ER_PLUGIN_CANNOT_BE_UNINSTALLED = 1883
ER_GTID_UNSAFE_BINLOG_SPLITTABLE_STATEMENT_AND_ASSIGNED_GTID = 1884
ER_SLAVE_HAS_MORE_GTIDS_THAN_MASTER = 1885
ER_MISSING_KEY = 1886
WARN_NAMED_PIPE_ACCESS_EVERYONE = 1887
ER_FILE_CORRUPT = 3000
ER_ERROR_ON_MASTER = 3001
OBSOLETE_ER_INCONSISTENT_ERROR = 3002
ER_STORAGE_ENGINE_NOT_LOADED = 3003
ER_GET_STACKED_DA_WITHOUT_ACTIVE_HANDLER = 3004
ER_WARN_LEGACY_SYNTAX_CONVERTED = 3005
ER_BINLOG_UNSAFE_FULLTEXT_PLUGIN = 3006
ER_CANNOT_DISCARD_TEMPORARY_TABLE = 3007
ER_FK_DEPTH_EXCEEDED = 3008
ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2 = 3009
ER_WARN_TRIGGER_DOESNT_HAVE_CREATED = 3010
ER_REFERENCED_TRG_DOES_NOT_EXIST = 3011
ER_EXPLAIN_NOT_SUPPORTED = 3012
ER_INVALID_FIELD_SIZE = 3013
ER_MISSING_HA_CREATE_OPTION = 3014
ER_ENGINE_OUT_OF_MEMORY = 3015
ER_PASSWORD_EXPIRE_ANONYMOUS_USER = 3016
ER_SLAVE_SQL_THREAD_MUST_STOP = 3017
ER_NO_FT_MATERIALIZED_SUBQUERY = 3018
ER_INNODB_UNDO_LOG_FULL = 3019
ER_INVALID_ARGUMENT_FOR_LOGARITHM = 3020
ER_SLAVE_CHANNEL_IO_THREAD_MUST_STOP = 3021
ER_WARN_OPEN_TEMP_TABLES_MUST_BE_ZERO = 3022
ER_WARN_ONLY_MASTER_LOG_FILE_NO_POS = 3023
ER_QUERY_TIMEOUT = 3024
ER_NON_RO_SELECT_DISABLE_TIMER = 3025
ER_DUP_LIST_ENTRY = 3026
OBSOLETE_ER_SQL_MODE_NO_EFFECT = 3027
ER_AGGREGATE_ORDER_FOR_UNION = 3028
ER_AGGREGATE_ORDER_NON_AGG_QUERY = 3029
ER_SLAVE_WORKER_STOPPED_PREVIOUS_THD_ERROR = 3030
ER_DONT_SUPPORT_REPLICA_PRESERVE_COMMIT_ORDER = 3031
ER_SERVER_OFFLINE_MODE = 3032
ER_GIS_DIFFERENT_SRIDS = 3033
ER_GIS_UNSUPPORTED_ARGUMENT = 3034
ER_GIS_UNKNOWN_ERROR = 3035
ER_GIS_UNKNOWN_EXCEPTION = 3036
ER_GIS_INVALID_DATA = 3037
ER_BOOST_GEOMETRY_EMPTY_INPUT_EXCEPTION = 3038
ER_BOOST_GEOMETRY_CENTROID_EXCEPTION = 3039
ER_BOOST_GEOMETRY_OVERLAY_INVALID_INPUT_EXCEPTION = 3040
ER_BOOST_GEOMETRY_TURN_INFO_EXCEPTION = 3041
ER_BOOST_GEOMETRY_SELF_INTERSECTION_POINT_EXCEPTION = 3042
ER_BOOST_GEOMETRY_UNKNOWN_EXCEPTION = 3043
ER_STD_BAD_ALLOC_ERROR = 3044
ER_STD_DOMAIN_ERROR = 3045
ER_STD_LENGTH_ERROR = 3046
ER_STD_INVALID_ARGUMENT = 3047
ER_STD_OUT_OF_RANGE_ERROR = 3048
ER_STD_OVERFLOW_ERROR = 3049
ER_STD_RANGE_ERROR = 3050
ER_STD_UNDERFLOW_ERROR = 3051
ER_STD_LOGIC_ERROR = 3052
ER_STD_RUNTIME_ERROR = 3053
ER_STD_UNKNOWN_EXCEPTION = 3054
ER_GIS_DATA_WRONG_ENDIANESS = 3055
ER_CHANGE_MASTER_PASSWORD_LENGTH = 3056
ER_USER_LOCK_WRONG_NAME = 3057
ER_USER_LOCK_DEADLOCK = 3058
ER_REPLACE_INACCESSIBLE_ROWS = 3059
ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_GIS = 3060
ER_ILLEGAL_USER_VAR = 3061
ER_GTID_MODE_OFF = 3062
OBSOLETE_ER_UNSUPPORTED_BY_REPLICATION_THREAD = 3063
ER_INCORRECT_TYPE = 3064
ER_FIELD_IN_ORDER_NOT_SELECT = 3065
ER_AGGREGATE_IN_ORDER_NOT_SELECT = 3066
ER_INVALID_RPL_WILD_TABLE_FILTER_PATTERN = 3067
ER_NET_OK_PACKET_TOO_LARGE = 3068
ER_INVALID_JSON_DATA = 3069
ER_INVALID_GEOJSON_MISSING_MEMBER = 3070
ER_INVALID_GEOJSON_WRONG_TYPE = 3071
ER_INVALID_GEOJSON_UNSPECIFIED = 3072
ER_DIMENSION_UNSUPPORTED = 3073
ER_SLAVE_CHANNEL_DOES_NOT_EXIST = 3074
OBSOLETE_ER_SLAVE_MULTIPLE_CHANNELS_HOST_PORT = 3075
ER_SLAVE_CHANNEL_NAME_INVALID_OR_TOO_LONG = 3076
ER_SLAVE_NEW_CHANNEL_WRONG_REPOSITORY = 3077
OBSOLETE_ER_SLAVE_CHANNEL_DELETE = 3078
ER_SLAVE_MULTIPLE_CHANNELS_CMD = 3079
ER_SLAVE_MAX_CHANNELS_EXCEEDED = 3080
ER_SLAVE_CHANNEL_MUST_STOP = 3081
ER_SLAVE_CHANNEL_NOT_RUNNING = 3082
ER_SLAVE_CHANNEL_WAS_RUNNING = 3083
ER_SLAVE_CHANNEL_WAS_NOT_RUNNING = 3084
ER_SLAVE_CHANNEL_SQL_THREAD_MUST_STOP = 3085
ER_SLAVE_CHANNEL_SQL_SKIP_COUNTER = 3086
ER_WRONG_FIELD_WITH_GROUP_V2 = 3087
ER_MIX_OF_GROUP_FUNC_AND_FIELDS_V2 = 3088
ER_WARN_DEPRECATED_SYSVAR_UPDATE = 3089
ER_WARN_DEPRECATED_SQLMODE = 3090
ER_CANNOT_LOG_PARTIAL_DROP_DATABASE_WITH_GTID = 3091
ER_GROUP_REPLICATION_CONFIGURATION = 3092
ER_GROUP_REPLICATION_RUNNING = 3093
ER_GROUP_REPLICATION_APPLIER_INIT_ERROR = 3094
ER_GROUP_REPLICATION_STOP_APPLIER_THREAD_TIMEOUT = 3095
ER_GROUP_REPLICATION_COMMUNICATION_LAYER_SESSION_ERROR = 3096
ER_GROUP_REPLICATION_COMMUNICATION_LAYER_JOIN_ERROR = 3097
ER_BEFORE_DML_VALIDATION_ERROR = 3098
ER_PREVENTS_VARIABLE_WITHOUT_RBR = 3099
ER_RUN_HOOK_ERROR = 3100
ER_TRANSACTION_ROLLBACK_DURING_COMMIT = 3101
ER_GENERATED_COLUMN_FUNCTION_IS_NOT_ALLOWED = 3102
ER_UNSUPPORTED_ALTER_INPLACE_ON_VIRTUAL_COLUMN = 3103
ER_WRONG_FK_OPTION_FOR_GENERATED_COLUMN = 3104
ER_NON_DEFAULT_VALUE_FOR_GENERATED_COLUMN = 3105
ER_UNSUPPORTED_ACTION_ON_GENERATED_COLUMN = 3106
ER_GENERATED_COLUMN_NON_PRIOR = 3107
ER_DEPENDENT_BY_GENERATED_COLUMN = 3108
ER_GENERATED_COLUMN_REF_AUTO_INC = 3109
ER_FEATURE_NOT_AVAILABLE = 3110
ER_CANT_SET_GTID_MODE = 3111
ER_CANT_USE_AUTO_POSITION_WITH_GTID_MODE_OFF = 3112
OBSOLETE_ER_CANT_REPLICATE_ANONYMOUS_WITH_AUTO_POSITION = 3113
OBSOLETE_ER_CANT_REPLICATE_ANONYMOUS_WITH_GTID_MODE_ON = 3114
OBSOLETE_ER_CANT_REPLICATE_GTID_WITH_GTID_MODE_OFF = 3115
ER_CANT_ENFORCE_GTID_CONSISTENCY_WITH_ONGOING_GTID_VIOLATING_TX = 3116
ER_ENFORCE_GTID_CONSISTENCY_WARN_WITH_ONGOING_GTID_VIOLATING_TX = 3117
ER_ACCOUNT_HAS_BEEN_LOCKED = 3118
ER_WRONG_TABLESPACE_NAME = 3119
ER_TABLESPACE_IS_NOT_EMPTY = 3120
ER_WRONG_FILE_NAME = 3121
ER_BOOST_GEOMETRY_INCONSISTENT_TURNS_EXCEPTION = 3122
ER_WARN_OPTIMIZER_HINT_SYNTAX_ERROR = 3123
ER_WARN_BAD_MAX_EXECUTION_TIME = 3124
ER_WARN_UNSUPPORTED_MAX_EXECUTION_TIME = 3125
ER_WARN_CONFLICTING_HINT = 3126
ER_WARN_UNKNOWN_QB_NAME = 3127
ER_UNRESOLVED_HINT_NAME = 3128
ER_WARN_ON_MODIFYING_GTID_EXECUTED_TABLE = 3129
ER_PLUGGABLE_PROTOCOL_COMMAND_NOT_SUPPORTED = 3130
ER_LOCKING_SERVICE_WRONG_NAME = 3131
ER_LOCKING_SERVICE_DEADLOCK = 3132
ER_LOCKING_SERVICE_TIMEOUT = 3133
ER_GIS_MAX_POINTS_IN_GEOMETRY_OVERFLOWED = 3134
ER_SQL_MODE_MERGED = 3135
ER_VTOKEN_PLUGIN_TOKEN_MISMATCH = 3136
ER_VTOKEN_PLUGIN_TOKEN_NOT_FOUND = 3137
ER_CANT_SET_VARIABLE_WHEN_OWNING_GTID = 3138
ER_SLAVE_CHANNEL_OPERATION_NOT_ALLOWED = 3139
ER_INVALID_JSON_TEXT = 3140
ER_INVALID_JSON_TEXT_IN_PARAM = 3141
ER_INVALID_JSON_BINARY_DATA = 3142
ER_INVALID_JSON_PATH = 3143
ER_INVALID_JSON_CHARSET = 3144
ER_INVALID_JSON_CHARSET_IN_FUNCTION = 3145
ER_INVALID_TYPE_FOR_JSON = 3146
ER_INVALID_CAST_TO_JSON = 3147
ER_INVALID_JSON_PATH_CHARSET = 3148
ER_INVALID_JSON_PATH_WILDCARD = 3149
ER_JSON_VALUE_TOO_BIG = 3150
ER_JSON_KEY_TOO_BIG = 3151
ER_JSON_USED_AS_KEY = 3152
ER_JSON_VACUOUS_PATH = 3153
ER_JSON_BAD_ONE_OR_ALL_ARG = 3154
ER_NUMERIC_JSON_VALUE_OUT_OF_RANGE = 3155
ER_INVALID_JSON_VALUE_FOR_CAST = 3156
ER_JSON_DOCUMENT_TOO_DEEP = 3157
ER_JSON_DOCUMENT_NULL_KEY = 3158
ER_SECURE_TRANSPORT_REQUIRED = 3159
ER_NO_SECURE_TRANSPORTS_CONFIGURED = 3160
ER_DISABLED_STORAGE_ENGINE = 3161
ER_USER_DOES_NOT_EXIST = 3162
ER_USER_ALREADY_EXISTS = 3163
ER_AUDIT_API_ABORT = 3164
ER_INVALID_JSON_PATH_ARRAY_CELL = 3165
ER_BUFPOOL_RESIZE_INPROGRESS = 3166
ER_FEATURE_DISABLED_SEE_DOC = 3167
ER_SERVER_ISNT_AVAILABLE = 3168
ER_SESSION_WAS_KILLED = 3169
ER_CAPACITY_EXCEEDED = 3170
ER_CAPACITY_EXCEEDED_IN_RANGE_OPTIMIZER = 3171
OBSOLETE_ER_TABLE_NEEDS_UPG_PART = 3172
ER_CANT_WAIT_FOR_EXECUTED_GTID_SET_WHILE_OWNING_A_GTID = 3173
ER_CANNOT_ADD_FOREIGN_BASE_COL_VIRTUAL = 3174
ER_CANNOT_CREATE_VIRTUAL_INDEX_CONSTRAINT = 3175
ER_ERROR_ON_MODIFYING_GTID_EXECUTED_TABLE = 3176
ER_LOCK_REFUSED_BY_ENGINE = 3177
ER_UNSUPPORTED_ALTER_ONLINE_ON_VIRTUAL_COLUMN = 3178
ER_MASTER_KEY_ROTATION_NOT_SUPPORTED_BY_SE = 3179
OBSOLETE_ER_MASTER_KEY_ROTATION_ERROR_BY_SE = 3180
ER_MASTER_KEY_ROTATION_BINLOG_FAILED = 3181
ER_MASTER_KEY_ROTATION_SE_UNAVAILABLE = 3182
ER_TABLESPACE_CANNOT_ENCRYPT = 3183
ER_INVALID_ENCRYPTION_OPTION = 3184
ER_CANNOT_FIND_KEY_IN_KEYRING = 3185
ER_CAPACITY_EXCEEDED_IN_PARSER = 3186
ER_UNSUPPORTED_ALTER_ENCRYPTION_INPLACE = 3187
ER_KEYRING_UDF_KEYRING_SERVICE_ERROR = 3188
ER_USER_COLUMN_OLD_LENGTH = 3189
ER_CANT_RESET_MASTER = 3190
ER_GROUP_REPLICATION_MAX_GROUP_SIZE = 3191
ER_CANNOT_ADD_FOREIGN_BASE_COL_STORED = 3192
ER_TABLE_REFERENCED = 3193
OBSOLETE_ER_PARTITION_ENGINE_DEPRECATED_FOR_TABLE = 3194
OBSOLETE_ER_WARN_USING_GEOMFROMWKB_TO_SET_SRID_ZERO = 3195
OBSOLETE_ER_WARN_USING_GEOMFROMWKB_TO_SET_SRID = 3196
ER_XA_RETRY = 3197
ER_KEYRING_AWS_UDF_AWS_KMS_ERROR = 3198
ER_BINLOG_UNSAFE_XA = 3199
ER_UDF_ERROR = 3200
ER_KEYRING_MIGRATION_FAILURE = 3201
ER_KEYRING_ACCESS_DENIED_ERROR = 3202
ER_KEYRING_MIGRATION_STATUS = 3203
OBSOLETE_ER_PLUGIN_FAILED_TO_OPEN_TABLES = 3204
OBSOLETE_ER_PLUGIN_FAILED_TO_OPEN_TABLE = 3205
OBSOLETE_ER_AUDIT_LOG_NO_KEYRING_PLUGIN_INSTALLED = 3206
OBSOLETE_ER_AUDIT_LOG_ENCRYPTION_PASSWORD_HAS_NOT_BEEN_SET = 3207
OBSOLETE_ER_AUDIT_LOG_COULD_NOT_CREATE_AES_KEY = 3208
OBSOLETE_ER_AUDIT_LOG_ENCRYPTION_PASSWORD_CANNOT_BE_FETCHED = 3209
OBSOLETE_ER_AUDIT_LOG_JSON_FILTERING_NOT_ENABLED = 3210
OBSOLETE_ER_AUDIT_LOG_UDF_INSUFFICIENT_PRIVILEGE = 3211
OBSOLETE_ER_AUDIT_LOG_SUPER_PRIVILEGE_REQUIRED = 3212
OBSOLETE_ER_COULD_NOT_REINITIALIZE_AUDIT_LOG_FILTERS = 3213
OBSOLETE_ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_TYPE = 3214
OBSOLETE_ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_COUNT = 3215
OBSOLETE_ER_AUDIT_LOG_HAS_NOT_BEEN_INSTALLED = 3216
OBSOLETE_ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_TYPE = 3217
ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_VALUE = 3218
OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_PARSING_ERROR = 3219
OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_NAME_CANNOT_BE_EMPTY = 3220
OBSOLETE_ER_AUDIT_LOG_JSON_USER_NAME_CANNOT_BE_EMPTY = 3221
OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_DOES_NOT_EXISTS = 3222
OBSOLETE_ER_AUDIT_LOG_USER_FIRST_CHARACTER_MUST_BE_ALPHANUMERIC = 3223
OBSOLETE_ER_AUDIT_LOG_USER_NAME_INVALID_CHARACTER = 3224
OBSOLETE_ER_AUDIT_LOG_HOST_NAME_INVALID_CHARACTER = 3225
OBSOLETE_ER_XA_REPLICATION_FILTERS = 3226
OBSOLETE_ER_CANT_OPEN_ERROR_LOG = 3227
OBSOLETE_ER_GROUPING_ON_TIMESTAMP_IN_DST = 3228
OBSOLETE_ER_CANT_START_SERVER_NAMED_PIPE = 3229
ER_WRITE_SET_EXCEEDS_LIMIT = 3230
ER_UNSUPPORT_COMPRESSED_TEMPORARY_TABLE = 3500
ER_ACL_OPERATION_FAILED = 3501
ER_UNSUPPORTED_INDEX_ALGORITHM = 3502
ER_NO_SUCH_DB = 3503
ER_TOO_BIG_ENUM = 3504
ER_TOO_LONG_SET_ENUM_VALUE = 3505
ER_INVALID_DD_OBJECT = 3506
ER_UPDATING_DD_TABLE = 3507
ER_INVALID_DD_OBJECT_ID = 3508
ER_INVALID_DD_OBJECT_NAME = 3509
ER_TABLESPACE_MISSING_WITH_NAME = 3510
ER_TOO_LONG_ROUTINE_COMMENT = 3511
ER_SP_LOAD_FAILED = 3512
ER_INVALID_BITWISE_OPERANDS_SIZE = 3513
ER_INVALID_BITWISE_AGGREGATE_OPERANDS_SIZE = 3514
ER_WARN_UNSUPPORTED_HINT = 3515
ER_UNEXPECTED_GEOMETRY_TYPE = 3516
ER_SRS_PARSE_ERROR = 3517
ER_SRS_PROJ_PARAMETER_MISSING = 3518
ER_WARN_SRS_NOT_FOUND = 3519
ER_SRS_NOT_CARTESIAN = 3520
ER_SRS_NOT_CARTESIAN_UNDEFINED = 3521
ER_PK_INDEX_CANT_BE_INVISIBLE = 3522
ER_UNKNOWN_AUTHID = 3523
ER_FAILED_ROLE_GRANT = 3524
ER_OPEN_ROLE_TABLES = 3525
ER_FAILED_DEFAULT_ROLES = 3526
ER_COMPONENTS_NO_SCHEME = 3527
ER_COMPONENTS_NO_SCHEME_SERVICE = 3528
ER_COMPONENTS_CANT_LOAD = 3529
ER_ROLE_NOT_GRANTED = 3530
ER_FAILED_REVOKE_ROLE = 3531
ER_RENAME_ROLE = 3532
ER_COMPONENTS_CANT_ACQUIRE_SERVICE_IMPLEMENTATION = 3533
ER_COMPONENTS_CANT_SATISFY_DEPENDENCY = 3534
ER_COMPONENTS_LOAD_CANT_REGISTER_SERVICE_IMPLEMENTATION = 3535
ER_COMPONENTS_LOAD_CANT_INITIALIZE = 3536
ER_COMPONENTS_UNLOAD_NOT_LOADED = 3537
ER_COMPONENTS_UNLOAD_CANT_DEINITIALIZE = 3538
ER_COMPONENTS_CANT_RELEASE_SERVICE = 3539
ER_COMPONENTS_UNLOAD_CANT_UNREGISTER_SERVICE = 3540
ER_COMPONENTS_CANT_UNLOAD = 3541
ER_WARN_UNLOAD_THE_NOT_PERSISTED = 3542
ER_COMPONENT_TABLE_INCORRECT = 3543
ER_COMPONENT_MANIPULATE_ROW_FAILED = 3544
ER_COMPONENTS_UNLOAD_DUPLICATE_IN_GROUP = 3545
ER_CANT_SET_GTID_PURGED_DUE_SETS_CONSTRAINTS = 3546
ER_CANNOT_LOCK_USER_MANAGEMENT_CACHES = 3547
ER_SRS_NOT_FOUND = 3548
ER_VARIABLE_NOT_PERSISTED = 3549
ER_IS_QUERY_INVALID_CLAUSE = 3550
ER_UNABLE_TO_STORE_STATISTICS = 3551
ER_NO_SYSTEM_SCHEMA_ACCESS = 3552
ER_NO_SYSTEM_TABLESPACE_ACCESS = 3553
ER_NO_SYSTEM_TABLE_ACCESS = 3554
ER_NO_SYSTEM_TABLE_ACCESS_FOR_DICTIONARY_TABLE = 3555
ER_NO_SYSTEM_TABLE_ACCESS_FOR_SYSTEM_TABLE = 3556
ER_NO_SYSTEM_TABLE_ACCESS_FOR_TABLE = 3557
ER_INVALID_OPTION_KEY = 3558
ER_INVALID_OPTION_VALUE = 3559
ER_INVALID_OPTION_KEY_VALUE_PAIR = 3560
ER_INVALID_OPTION_START_CHARACTER = 3561
ER_INVALID_OPTION_END_CHARACTER = 3562
ER_INVALID_OPTION_CHARACTERS = 3563
ER_DUPLICATE_OPTION_KEY = 3564
ER_WARN_SRS_NOT_FOUND_AXIS_ORDER = 3565
ER_NO_ACCESS_TO_NATIVE_FCT = 3566
ER_RESET_MASTER_TO_VALUE_OUT_OF_RANGE = 3567
ER_UNRESOLVED_TABLE_LOCK = 3568
ER_DUPLICATE_TABLE_LOCK = 3569
ER_BINLOG_UNSAFE_SKIP_LOCKED = 3570
ER_BINLOG_UNSAFE_NOWAIT = 3571
ER_LOCK_NOWAIT = 3572
ER_CTE_RECURSIVE_REQUIRES_UNION = 3573
ER_CTE_RECURSIVE_REQUIRES_NONRECURSIVE_FIRST = 3574
ER_CTE_RECURSIVE_FORBIDS_AGGREGATION = 3575
ER_CTE_RECURSIVE_FORBIDDEN_JOIN_ORDER = 3576
ER_CTE_RECURSIVE_REQUIRES_SINGLE_REFERENCE = 3577
ER_SWITCH_TMP_ENGINE = 3578
ER_WINDOW_NO_SUCH_WINDOW = 3579
ER_WINDOW_CIRCULARITY_IN_WINDOW_GRAPH = 3580
ER_WINDOW_NO_CHILD_PARTITIONING = 3581
ER_WINDOW_NO_INHERIT_FRAME = 3582
ER_WINDOW_NO_REDEFINE_ORDER_BY = 3583
ER_WINDOW_FRAME_START_ILLEGAL = 3584
ER_WINDOW_FRAME_END_ILLEGAL = 3585
ER_WINDOW_FRAME_ILLEGAL = 3586
ER_WINDOW_RANGE_FRAME_ORDER_TYPE = 3587
ER_WINDOW_RANGE_FRAME_TEMPORAL_TYPE = 3588
ER_WINDOW_RANGE_FRAME_NUMERIC_TYPE = 3589
ER_WINDOW_RANGE_BOUND_NOT_CONSTANT = 3590
ER_WINDOW_DUPLICATE_NAME = 3591
ER_WINDOW_ILLEGAL_ORDER_BY = 3592
ER_WINDOW_INVALID_WINDOW_FUNC_USE = 3593
ER_WINDOW_INVALID_WINDOW_FUNC_ALIAS_USE = 3594
ER_WINDOW_NESTED_WINDOW_FUNC_USE_IN_WINDOW_SPEC = 3595
ER_WINDOW_ROWS_INTERVAL_USE = 3596
ER_WINDOW_NO_GROUP_ORDER_UNUSED = 3597
ER_WINDOW_EXPLAIN_JSON = 3598
ER_WINDOW_FUNCTION_IGNORES_FRAME = 3599
ER_WL9236_NOW_UNUSED = 3600
ER_INVALID_NO_OF_ARGS = 3601
ER_FIELD_IN_GROUPING_NOT_GROUP_BY = 3602
ER_TOO_LONG_TABLESPACE_COMMENT = 3603
ER_ENGINE_CANT_DROP_TABLE = 3604
ER_ENGINE_CANT_DROP_MISSING_TABLE = 3605
ER_TABLESPACE_DUP_FILENAME = 3606
ER_DB_DROP_RMDIR2 = 3607
ER_IMP_NO_FILES_MATCHED = 3608
ER_IMP_SCHEMA_DOES_NOT_EXIST = 3609
ER_IMP_TABLE_ALREADY_EXISTS = 3610
ER_IMP_INCOMPATIBLE_MYSQLD_VERSION = 3611
ER_IMP_INCOMPATIBLE_DD_VERSION = 3612
ER_IMP_INCOMPATIBLE_SDI_VERSION = 3613
ER_WARN_INVALID_HINT = 3614
ER_VAR_DOES_NOT_EXIST = 3615
ER_LONGITUDE_OUT_OF_RANGE = 3616
ER_LATITUDE_OUT_OF_RANGE = 3617
ER_NOT_IMPLEMENTED_FOR_GEOGRAPHIC_SRS = 3618
ER_ILLEGAL_PRIVILEGE_LEVEL = 3619
ER_NO_SYSTEM_VIEW_ACCESS = 3620
ER_COMPONENT_FILTER_FLABBERGASTED = 3621
ER_PART_EXPR_TOO_LONG = 3622
ER_UDF_DROP_DYNAMICALLY_REGISTERED = 3623
ER_UNABLE_TO_STORE_COLUMN_STATISTICS = 3624
ER_UNABLE_TO_UPDATE_COLUMN_STATISTICS = 3625
ER_UNABLE_TO_DROP_COLUMN_STATISTICS = 3626
ER_UNABLE_TO_BUILD_HISTOGRAM = 3627
ER_MANDATORY_ROLE = 3628
ER_MISSING_TABLESPACE_FILE = 3629
ER_PERSIST_ONLY_ACCESS_DENIED_ERROR = 3630
ER_CMD_NEED_SUPER = 3631
ER_PATH_IN_DATADIR = 3632
ER_CLONE_DDL_IN_PROGRESS = 3633
ER_CLONE_TOO_MANY_CONCURRENT_CLONES = 3634
ER_APPLIER_LOG_EVENT_VALIDATION_ERROR = 3635
ER_CTE_MAX_RECURSION_DEPTH = 3636
ER_NOT_HINT_UPDATABLE_VARIABLE = 3637
ER_CREDENTIALS_CONTRADICT_TO_HISTORY = 3638
ER_WARNING_PASSWORD_HISTORY_CLAUSES_VOID = 3639
ER_CLIENT_DOES_NOT_SUPPORT = 3640
ER_I_S_SKIPPED_TABLESPACE = 3641
ER_TABLESPACE_ENGINE_MISMATCH = 3642
ER_WRONG_SRID_FOR_COLUMN = 3643
ER_CANNOT_ALTER_SRID_DUE_TO_INDEX = 3644
ER_WARN_BINLOG_PARTIAL_UPDATES_DISABLED = 3645
ER_WARN_BINLOG_V1_ROW_EVENTS_DISABLED = 3646
ER_WARN_BINLOG_PARTIAL_UPDATES_SUGGESTS_PARTIAL_IMAGES = 3647
ER_COULD_NOT_APPLY_JSON_DIFF = 3648
ER_CORRUPTED_JSON_DIFF = 3649
ER_RESOURCE_GROUP_EXISTS = 3650
ER_RESOURCE_GROUP_NOT_EXISTS = 3651
ER_INVALID_VCPU_ID = 3652
ER_INVALID_VCPU_RANGE = 3653
ER_INVALID_THREAD_PRIORITY = 3654
ER_DISALLOWED_OPERATION = 3655
ER_RESOURCE_GROUP_BUSY = 3656
ER_RESOURCE_GROUP_DISABLED = 3657
ER_FEATURE_UNSUPPORTED = 3658
ER_ATTRIBUTE_IGNORED = 3659
ER_INVALID_THREAD_ID = 3660
ER_RESOURCE_GROUP_BIND_FAILED = 3661
ER_INVALID_USE_OF_FORCE_OPTION = 3662
ER_GROUP_REPLICATION_COMMAND_FAILURE = 3663
ER_SDI_OPERATION_FAILED = 3664
ER_MISSING_JSON_TABLE_VALUE = 3665
ER_WRONG_JSON_TABLE_VALUE = 3666
ER_TF_MUST_HAVE_ALIAS = 3667
ER_TF_FORBIDDEN_JOIN_TYPE = 3668
ER_JT_VALUE_OUT_OF_RANGE = 3669
ER_JT_MAX_NESTED_PATH = 3670
ER_PASSWORD_EXPIRATION_NOT_SUPPORTED_BY_AUTH_METHOD = 3671
ER_INVALID_GEOJSON_CRS_NOT_TOP_LEVEL = 3672
ER_BAD_NULL_ERROR_NOT_IGNORED = 3673
WARN_USELESS_SPATIAL_INDEX = 3674
ER_DISK_FULL_NOWAIT = 3675
ER_PARSE_ERROR_IN_DIGEST_FN = 3676
ER_UNDISCLOSED_PARSE_ERROR_IN_DIGEST_FN = 3677
ER_SCHEMA_DIR_EXISTS = 3678
ER_SCHEMA_DIR_MISSING = 3679
ER_SCHEMA_DIR_CREATE_FAILED = 3680
ER_SCHEMA_DIR_UNKNOWN = 3681
ER_ONLY_IMPLEMENTED_FOR_SRID_0_AND_4326 = 3682
ER_BINLOG_EXPIRE_LOG_DAYS_AND_SECS_USED_TOGETHER = 3683
ER_REGEXP_BUFFER_OVERFLOW = 3684
ER_REGEXP_ILLEGAL_ARGUMENT = 3685
ER_REGEXP_INDEX_OUTOFBOUNDS_ERROR = 3686
ER_REGEXP_INTERNAL_ERROR = 3687
ER_REGEXP_RULE_SYNTAX = 3688
ER_REGEXP_BAD_ESCAPE_SEQUENCE = 3689
ER_REGEXP_UNIMPLEMENTED = 3690
ER_REGEXP_MISMATCHED_PAREN = 3691
ER_REGEXP_BAD_INTERVAL = 3692
ER_REGEXP_MAX_LT_MIN = 3693
ER_REGEXP_INVALID_BACK_REF = 3694
ER_REGEXP_LOOK_BEHIND_LIMIT = 3695
ER_REGEXP_MISSING_CLOSE_BRACKET = 3696
ER_REGEXP_INVALID_RANGE = 3697
ER_REGEXP_STACK_OVERFLOW = 3698
ER_REGEXP_TIME_OUT = 3699
ER_REGEXP_PATTERN_TOO_BIG = 3700
ER_CANT_SET_ERROR_LOG_SERVICE = 3701
ER_EMPTY_PIPELINE_FOR_ERROR_LOG_SERVICE = 3702
ER_COMPONENT_FILTER_DIAGNOSTICS = 3703
ER_NOT_IMPLEMENTED_FOR_CARTESIAN_SRS = 3704
ER_NOT_IMPLEMENTED_FOR_PROJECTED_SRS = 3705
ER_NONPOSITIVE_RADIUS = 3706
ER_RESTART_SERVER_FAILED = 3707
ER_SRS_MISSING_MANDATORY_ATTRIBUTE = 3708
ER_SRS_MULTIPLE_ATTRIBUTE_DEFINITIONS = 3709
ER_SRS_NAME_CANT_BE_EMPTY_OR_WHITESPACE = 3710
ER_SRS_ORGANIZATION_CANT_BE_EMPTY_OR_WHITESPACE = 3711
ER_SRS_ID_ALREADY_EXISTS = 3712
ER_WARN_SRS_ID_ALREADY_EXISTS = 3713
ER_CANT_MODIFY_SRID_0 = 3714
ER_WARN_RESERVED_SRID_RANGE = 3715
ER_CANT_MODIFY_SRS_USED_BY_COLUMN = 3716
ER_SRS_INVALID_CHARACTER_IN_ATTRIBUTE = 3717
ER_SRS_ATTRIBUTE_STRING_TOO_LONG = 3718
ER_DEPRECATED_UTF8_ALIAS = 3719
ER_DEPRECATED_NATIONAL = 3720
ER_INVALID_DEFAULT_UTF8MB4_COLLATION = 3721
ER_UNABLE_TO_COLLECT_LOG_STATUS = 3722
ER_RESERVED_TABLESPACE_NAME = 3723
ER_UNABLE_TO_SET_OPTION = 3724
ER_SLAVE_POSSIBLY_DIVERGED_AFTER_DDL = 3725
ER_SRS_NOT_GEOGRAPHIC = 3726
ER_POLYGON_TOO_LARGE = 3727
ER_SPATIAL_UNIQUE_INDEX = 3728
ER_INDEX_TYPE_NOT_SUPPORTED_FOR_SPATIAL_INDEX = 3729
ER_FK_CANNOT_DROP_PARENT = 3730
ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE = 3731
ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE = 3732
ER_FK_CANNOT_USE_VIRTUAL_COLUMN = 3733
ER_FK_NO_COLUMN_PARENT = 3734
ER_CANT_SET_ERROR_SUPPRESSION_LIST = 3735
ER_SRS_GEOGCS_INVALID_AXES = 3736
ER_SRS_INVALID_SEMI_MAJOR_AXIS = 3737
ER_SRS_INVALID_INVERSE_FLATTENING = 3738
ER_SRS_INVALID_ANGULAR_UNIT = 3739
ER_SRS_INVALID_PRIME_MERIDIAN = 3740
ER_TRANSFORM_SOURCE_SRS_NOT_SUPPORTED = 3741
ER_TRANSFORM_TARGET_SRS_NOT_SUPPORTED = 3742
ER_TRANSFORM_SOURCE_SRS_MISSING_TOWGS84 = 3743
ER_TRANSFORM_TARGET_SRS_MISSING_TOWGS84 = 3744
ER_TEMP_TABLE_PREVENTS_SWITCH_SESSION_BINLOG_FORMAT = 3745
ER_TEMP_TABLE_PREVENTS_SWITCH_GLOBAL_BINLOG_FORMAT = 3746
ER_RUNNING_APPLIER_PREVENTS_SWITCH_GLOBAL_BINLOG_FORMAT = 3747
ER_CLIENT_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRX_IN_SBR = 3748
OBSOLETE_ER_XA_CANT_CREATE_MDL_BACKUP = 3749
ER_TABLE_WITHOUT_PK = 3750
ER_WARN_DATA_TRUNCATED_FUNCTIONAL_INDEX = 3751
ER_WARN_DATA_OUT_OF_RANGE_FUNCTIONAL_INDEX = 3752
ER_FUNCTIONAL_INDEX_ON_JSON_OR_GEOMETRY_FUNCTION = 3753
ER_FUNCTIONAL_INDEX_REF_AUTO_INCREMENT = 3754
ER_CANNOT_DROP_COLUMN_FUNCTIONAL_INDEX = 3755
ER_FUNCTIONAL_INDEX_PRIMARY_KEY = 3756
ER_FUNCTIONAL_INDEX_ON_LOB = 3757
ER_FUNCTIONAL_INDEX_FUNCTION_IS_NOT_ALLOWED = 3758
ER_FULLTEXT_FUNCTIONAL_INDEX = 3759
ER_SPATIAL_FUNCTIONAL_INDEX = 3760
ER_WRONG_KEY_COLUMN_FUNCTIONAL_INDEX = 3761
ER_FUNCTIONAL_INDEX_ON_FIELD = 3762
ER_GENERATED_COLUMN_NAMED_FUNCTION_IS_NOT_ALLOWED = 3763
ER_GENERATED_COLUMN_ROW_VALUE = 3764
ER_GENERATED_COLUMN_VARIABLES = 3765
ER_DEPENDENT_BY_DEFAULT_GENERATED_VALUE = 3766
ER_DEFAULT_VAL_GENERATED_NON_PRIOR = 3767
ER_DEFAULT_VAL_GENERATED_REF_AUTO_INC = 3768
ER_DEFAULT_VAL_GENERATED_FUNCTION_IS_NOT_ALLOWED = 3769
ER_DEFAULT_VAL_GENERATED_NAMED_FUNCTION_IS_NOT_ALLOWED = 3770
ER_DEFAULT_VAL_GENERATED_ROW_VALUE = 3771
ER_DEFAULT_VAL_GENERATED_VARIABLES = 3772
ER_DEFAULT_AS_VAL_GENERATED = 3773
ER_UNSUPPORTED_ACTION_ON_DEFAULT_VAL_GENERATED = 3774
ER_GTID_UNSAFE_ALTER_ADD_COL_WITH_DEFAULT_EXPRESSION = 3775
ER_FK_CANNOT_CHANGE_ENGINE = 3776
ER_WARN_DEPRECATED_USER_SET_EXPR = 3777
ER_WARN_DEPRECATED_UTF8MB3_COLLATION = 3778
ER_WARN_DEPRECATED_NESTED_COMMENT_SYNTAX = 3779
ER_FK_INCOMPATIBLE_COLUMNS = 3780
ER_GR_HOLD_WAIT_TIMEOUT = 3781
ER_GR_HOLD_KILLED = 3782
ER_GR_HOLD_MEMBER_STATUS_ERROR = 3783
ER_RPL_ENCRYPTION_FAILED_TO_FETCH_KEY = 3784
ER_RPL_ENCRYPTION_KEY_NOT_FOUND = 3785
ER_RPL_ENCRYPTION_KEYRING_INVALID_KEY = 3786
ER_RPL_ENCRYPTION_HEADER_ERROR = 3787
ER_RPL_ENCRYPTION_FAILED_TO_ROTATE_LOGS = 3788
ER_RPL_ENCRYPTION_KEY_EXISTS_UNEXPECTED = 3789
ER_RPL_ENCRYPTION_FAILED_TO_GENERATE_KEY = 3790
ER_RPL_ENCRYPTION_FAILED_TO_STORE_KEY = 3791
ER_RPL_ENCRYPTION_FAILED_TO_REMOVE_KEY = 3792
ER_RPL_ENCRYPTION_UNABLE_TO_CHANGE_OPTION = 3793
ER_RPL_ENCRYPTION_MASTER_KEY_RECOVERY_FAILED = 3794
ER_SLOW_LOG_MODE_IGNORED_WHEN_NOT_LOGGING_TO_FILE = 3795
ER_GRP_TRX_CONSISTENCY_NOT_ALLOWED = 3796
ER_GRP_TRX_CONSISTENCY_BEFORE = 3797
ER_GRP_TRX_CONSISTENCY_AFTER_ON_TRX_BEGIN = 3798
ER_GRP_TRX_CONSISTENCY_BEGIN_NOT_ALLOWED = 3799
ER_FUNCTIONAL_INDEX_ROW_VALUE_IS_NOT_ALLOWED = 3800
ER_RPL_ENCRYPTION_FAILED_TO_ENCRYPT = 3801
ER_PAGE_TRACKING_NOT_STARTED = 3802
ER_PAGE_TRACKING_RANGE_NOT_TRACKED = 3803
ER_PAGE_TRACKING_CANNOT_PURGE = 3804
ER_RPL_ENCRYPTION_CANNOT_ROTATE_BINLOG_MASTER_KEY = 3805
ER_BINLOG_MASTER_KEY_RECOVERY_OUT_OF_COMBINATION = 3806
ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_OPERATE_KEY = 3807
ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_ROTATE_LOGS = 3808
ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_REENCRYPT_LOG = 3809
ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_UNUSED_KEYS = 3810
ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_AUX_KEY = 3811
ER_NON_BOOLEAN_EXPR_FOR_CHECK_CONSTRAINT = 3812
ER_COLUMN_CHECK_CONSTRAINT_REFERENCES_OTHER_COLUMN = 3813
ER_CHECK_CONSTRAINT_NAMED_FUNCTION_IS_NOT_ALLOWED = 3814
ER_CHECK_CONSTRAINT_FUNCTION_IS_NOT_ALLOWED = 3815
ER_CHECK_CONSTRAINT_VARIABLES = 3816
ER_CHECK_CONSTRAINT_ROW_VALUE = 3817
ER_CHECK_CONSTRAINT_REFERS_AUTO_INCREMENT_COLUMN = 3818
ER_CHECK_CONSTRAINT_VIOLATED = 3819
ER_CHECK_CONSTRAINT_REFERS_UNKNOWN_COLUMN = 3820
ER_CHECK_CONSTRAINT_NOT_FOUND = 3821
ER_CHECK_CONSTRAINT_DUP_NAME = 3822
ER_CHECK_CONSTRAINT_CLAUSE_USING_FK_REFER_ACTION_COLUMN = 3823
WARN_UNENCRYPTED_TABLE_IN_ENCRYPTED_DB = 3824
ER_INVALID_ENCRYPTION_REQUEST = 3825
ER_CANNOT_SET_TABLE_ENCRYPTION = 3826
ER_CANNOT_SET_DATABASE_ENCRYPTION = 3827
ER_CANNOT_SET_TABLESPACE_ENCRYPTION = 3828
ER_TABLESPACE_CANNOT_BE_ENCRYPTED = 3829
ER_TABLESPACE_CANNOT_BE_DECRYPTED = 3830
ER_TABLESPACE_TYPE_UNKNOWN = 3831
ER_TARGET_TABLESPACE_UNENCRYPTED = 3832
ER_CANNOT_USE_ENCRYPTION_CLAUSE = 3833
ER_INVALID_MULTIPLE_CLAUSES = 3834
ER_UNSUPPORTED_USE_OF_GRANT_AS = 3835
ER_UKNOWN_AUTH_ID_OR_ACCESS_DENIED_FOR_GRANT_AS = 3836
ER_DEPENDENT_BY_FUNCTIONAL_INDEX = 3837
ER_PLUGIN_NOT_EARLY = 3838
ER_INNODB_REDO_LOG_ARCHIVE_START_SUBDIR_PATH = 3839
ER_INNODB_REDO_LOG_ARCHIVE_START_TIMEOUT = 3840
ER_INNODB_REDO_LOG_ARCHIVE_DIRS_INVALID = 3841
ER_INNODB_REDO_LOG_ARCHIVE_LABEL_NOT_FOUND = 3842
ER_INNODB_REDO_LOG_ARCHIVE_DIR_EMPTY = 3843
ER_INNODB_REDO_LOG_ARCHIVE_NO_SUCH_DIR = 3844
ER_INNODB_REDO_LOG_ARCHIVE_DIR_CLASH = 3845
ER_INNODB_REDO_LOG_ARCHIVE_DIR_PERMISSIONS = 3846
ER_INNODB_REDO_LOG_ARCHIVE_FILE_CREATE = 3847
ER_INNODB_REDO_LOG_ARCHIVE_ACTIVE = 3848
ER_INNODB_REDO_LOG_ARCHIVE_INACTIVE = 3849
ER_INNODB_REDO_LOG_ARCHIVE_FAILED = 3850
ER_INNODB_REDO_LOG_ARCHIVE_SESSION = 3851
ER_STD_REGEX_ERROR = 3852
ER_INVALID_JSON_TYPE = 3853
ER_CANNOT_CONVERT_STRING = 3854
ER_DEPENDENT_BY_PARTITION_FUNC = 3855
ER_WARN_DEPRECATED_FLOAT_AUTO_INCREMENT = 3856
ER_RPL_CANT_STOP_SLAVE_WHILE_LOCKED_BACKUP = 3857
ER_WARN_DEPRECATED_FLOAT_DIGITS = 3858
ER_WARN_DEPRECATED_FLOAT_UNSIGNED = 3859
ER_WARN_DEPRECATED_INTEGER_DISPLAY_WIDTH = 3860
ER_WARN_DEPRECATED_ZEROFILL = 3861
ER_CLONE_DONOR = 3862
ER_CLONE_PROTOCOL = 3863
ER_CLONE_DONOR_VERSION = 3864
ER_CLONE_OS = 3865
ER_CLONE_PLATFORM = 3866
ER_CLONE_CHARSET = 3867
ER_CLONE_CONFIG = 3868
ER_CLONE_SYS_CONFIG = 3869
ER_CLONE_PLUGIN_MATCH = 3870
ER_CLONE_LOOPBACK = 3871
ER_CLONE_ENCRYPTION = 3872
ER_CLONE_DISK_SPACE = 3873
ER_CLONE_IN_PROGRESS = 3874
ER_CLONE_DISALLOWED = 3875
ER_CANNOT_GRANT_ROLES_TO_ANONYMOUS_USER = 3876
ER_SECONDARY_ENGINE_PLUGIN = 3877
ER_SECOND_PASSWORD_CANNOT_BE_EMPTY = 3878
ER_DB_ACCESS_DENIED = 3879
ER_DA_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES = 3880
ER_DA_RPL_GTID_TABLE_CANNOT_OPEN = 3881
ER_GEOMETRY_IN_UNKNOWN_LENGTH_UNIT = 3882
ER_DA_PLUGIN_INSTALL_ERROR = 3883
ER_NO_SESSION_TEMP = 3884
ER_DA_UNKNOWN_ERROR_NUMBER = 3885
ER_COLUMN_CHANGE_SIZE = 3886
ER_REGEXP_INVALID_CAPTURE_GROUP_NAME = 3887
ER_DA_SSL_LIBRARY_ERROR = 3888
ER_SECONDARY_ENGINE = 3889
ER_SECONDARY_ENGINE_DDL = 3890
ER_INCORRECT_CURRENT_PASSWORD = 3891
ER_MISSING_CURRENT_PASSWORD = 3892
ER_CURRENT_PASSWORD_NOT_REQUIRED = 3893
ER_PASSWORD_CANNOT_BE_RETAINED_ON_PLUGIN_CHANGE = 3894
ER_CURRENT_PASSWORD_CANNOT_BE_RETAINED = 3895
ER_PARTIAL_REVOKES_EXIST = 3896
ER_CANNOT_GRANT_SYSTEM_PRIV_TO_MANDATORY_ROLE = 3897
ER_XA_REPLICATION_FILTERS = 3898
ER_UNSUPPORTED_SQL_MODE = 3899
ER_REGEXP_INVALID_FLAG = 3900
ER_PARTIAL_REVOKE_AND_DB_GRANT_BOTH_EXISTS = 3901
ER_UNIT_NOT_FOUND = 3902
ER_INVALID_JSON_VALUE_FOR_FUNC_INDEX = 3903
ER_JSON_VALUE_OUT_OF_RANGE_FOR_FUNC_INDEX = 3904
ER_EXCEEDED_MV_KEYS_NUM = 3905
ER_EXCEEDED_MV_KEYS_SPACE = 3906
ER_FUNCTIONAL_INDEX_DATA_IS_TOO_LONG = 3907
ER_WRONG_MVI_VALUE = 3908
ER_WARN_FUNC_INDEX_NOT_APPLICABLE = 3909
ER_GRP_RPL_UDF_ERROR = 3910
ER_UPDATE_GTID_PURGED_WITH_GR = 3911
ER_GROUPING_ON_TIMESTAMP_IN_DST = 3912
ER_TABLE_NAME_CAUSES_TOO_LONG_PATH = 3913
ER_AUDIT_LOG_INSUFFICIENT_PRIVILEGE = 3914
OBSOLETE_ER_AUDIT_LOG_PASSWORD_HAS_BEEN_COPIED = 3915
ER_DA_GRP_RPL_STARTED_AUTO_REJOIN = 3916
ER_SYSVAR_CHANGE_DURING_QUERY = 3917
ER_GLOBSTAT_CHANGE_DURING_QUERY = 3918
ER_GRP_RPL_MESSAGE_SERVICE_INIT_FAILURE = 3919
ER_CHANGE_MASTER_WRONG_COMPRESSION_ALGORITHM_CLIENT = 3920
ER_CHANGE_MASTER_WRONG_COMPRESSION_LEVEL_CLIENT = 3921
ER_WRONG_COMPRESSION_ALGORITHM_CLIENT = 3922
ER_WRONG_COMPRESSION_LEVEL_CLIENT = 3923
ER_CHANGE_MASTER_WRONG_COMPRESSION_ALGORITHM_LIST_CLIENT = 3924
ER_CLIENT_PRIVILEGE_CHECKS_USER_CANNOT_BE_ANONYMOUS = 3925
ER_CLIENT_PRIVILEGE_CHECKS_USER_DOES_NOT_EXIST = 3926
ER_CLIENT_PRIVILEGE_CHECKS_USER_CORRUPT = 3927
ER_CLIENT_PRIVILEGE_CHECKS_USER_NEEDS_RPL_APPLIER_PRIV = 3928
ER_WARN_DA_PRIVILEGE_NOT_REGISTERED = 3929
ER_CLIENT_KEYRING_UDF_KEY_INVALID = 3930
ER_CLIENT_KEYRING_UDF_KEY_TYPE_INVALID = 3931
ER_CLIENT_KEYRING_UDF_KEY_TOO_LONG = 3932
ER_CLIENT_KEYRING_UDF_KEY_TYPE_TOO_LONG = 3933
ER_JSON_SCHEMA_VALIDATION_ERROR_WITH_DETAILED_REPORT = 3934
ER_DA_UDF_INVALID_CHARSET_SPECIFIED = 3935
ER_DA_UDF_INVALID_CHARSET = 3936
ER_DA_UDF_INVALID_COLLATION = 3937
ER_DA_UDF_INVALID_EXTENSION_ARGUMENT_TYPE = 3938
ER_MULTIPLE_CONSTRAINTS_WITH_SAME_NAME = 3939
ER_CONSTRAINT_NOT_FOUND = 3940
ER_ALTER_CONSTRAINT_ENFORCEMENT_NOT_SUPPORTED = 3941
ER_TABLE_VALUE_CONSTRUCTOR_MUST_HAVE_COLUMNS = 3942
ER_TABLE_VALUE_CONSTRUCTOR_CANNOT_HAVE_DEFAULT = 3943
ER_CLIENT_QUERY_FAILURE_INVALID_NON_ROW_FORMAT = 3944
ER_REQUIRE_ROW_FORMAT_INVALID_VALUE = 3945
ER_FAILED_TO_DETERMINE_IF_ROLE_IS_MANDATORY = 3946
ER_FAILED_TO_FETCH_MANDATORY_ROLE_LIST = 3947
ER_CLIENT_LOCAL_FILES_DISABLED = 3948
ER_IMP_INCOMPATIBLE_CFG_VERSION = 3949
ER_DA_OOM = 3950
ER_DA_UDF_INVALID_ARGUMENT_TO_SET_CHARSET = 3951
ER_DA_UDF_INVALID_RETURN_TYPE_TO_SET_CHARSET = 3952
ER_MULTIPLE_INTO_CLAUSES = 3953
ER_MISPLACED_INTO = 3954
ER_USER_ACCESS_DENIED_FOR_USER_ACCOUNT_BLOCKED_BY_PASSWORD_LOCK = 3955
ER_WARN_DEPRECATED_YEAR_UNSIGNED = 3956
ER_CLONE_NETWORK_PACKET = 3957
ER_SDI_OPERATION_FAILED_MISSING_RECORD = 3958
ER_DEPENDENT_BY_CHECK_CONSTRAINT = 3959
ER_GRP_OPERATION_NOT_ALLOWED_GR_MUST_STOP = 3960
ER_WARN_DEPRECATED_JSON_TABLE_ON_ERROR_ON_EMPTY = 3961
ER_WARN_DEPRECATED_INNER_INTO = 3962
ER_WARN_DEPRECATED_VALUES_FUNCTION_ALWAYS_NULL = 3963
ER_WARN_DEPRECATED_SQL_CALC_FOUND_ROWS = 3964
ER_WARN_DEPRECATED_FOUND_ROWS = 3965
ER_MISSING_JSON_VALUE = 3966
ER_MULTIPLE_JSON_VALUES = 3967
ER_HOSTNAME_TOO_LONG = 3968
ER_WARN_CLIENT_DEPRECATED_PARTITION_PREFIX_KEY = 3969
ER_GROUP_REPLICATION_USER_EMPTY_MSG = 3970
ER_GROUP_REPLICATION_USER_MANDATORY_MSG = 3971
ER_GROUP_REPLICATION_PASSWORD_LENGTH = 3972
ER_SUBQUERY_TRANSFORM_REJECTED = 3973
ER_DA_GRP_RPL_RECOVERY_ENDPOINT_FORMAT = 3974
ER_DA_GRP_RPL_RECOVERY_ENDPOINT_INVALID = 3975
ER_WRONG_VALUE_FOR_VAR_PLUS_ACTIONABLE_PART = 3976
ER_STATEMENT_NOT_ALLOWED_AFTER_START_TRANSACTION = 3977
ER_FOREIGN_KEY_WITH_ATOMIC_CREATE_SELECT = 3978
ER_NOT_ALLOWED_WITH_START_TRANSACTION = 3979
ER_INVALID_JSON_ATTRIBUTE = 3980
ER_ENGINE_ATTRIBUTE_NOT_SUPPORTED = 3981
ER_INVALID_USER_ATTRIBUTE_JSON = 3982
ER_INNODB_REDO_DISABLED = 3983
ER_INNODB_REDO_ARCHIVING_ENABLED = 3984
ER_MDL_OUT_OF_RESOURCES = 3985
ER_IMPLICIT_COMPARISON_FOR_JSON = 3986
ER_FUNCTION_DOES_NOT_SUPPORT_CHARACTER_SET = 3987
ER_IMPOSSIBLE_STRING_CONVERSION = 3988
ER_SCHEMA_READ_ONLY = 3989
ER_RPL_ASYNC_RECONNECT_GTID_MODE_OFF = 3990
ER_RPL_ASYNC_RECONNECT_AUTO_POSITION_OFF = 3991
ER_DISABLE_GTID_MODE_REQUIRES_ASYNC_RECONNECT_OFF = 3992
ER_DISABLE_AUTO_POSITION_REQUIRES_ASYNC_RECONNECT_OFF = 3993
ER_INVALID_PARAMETER_USE = 3994
ER_CHARACTER_SET_MISMATCH = 3995
ER_WARN_VAR_VALUE_CHANGE_NOT_SUPPORTED = 3996
ER_INVALID_TIME_ZONE_INTERVAL = 3997
ER_INVALID_CAST = 3998
ER_HYPERGRAPH_NOT_SUPPORTED_YET = 3999
ER_WARN_HYPERGRAPH_EXPERIMENTAL = 4000
ER_DA_NO_ERROR_LOG_PARSER_CONFIGURED = 4001
ER_DA_ERROR_LOG_TABLE_DISABLED = 4002
ER_DA_ERROR_LOG_MULTIPLE_FILTERS = 4003
ER_DA_CANT_OPEN_ERROR_LOG = 4004
ER_USER_REFERENCED_AS_DEFINER = 4005
ER_CANNOT_USER_REFERENCED_AS_DEFINER = 4006
ER_REGEX_NUMBER_TOO_BIG = 4007
ER_SPVAR_NONINTEGER_TYPE = 4008
WARN_UNSUPPORTED_ACL_TABLES_READ = 4009
ER_BINLOG_UNSAFE_ACL_TABLE_READ_IN_DML_DDL = 4010
ER_STOP_REPLICA_MONITOR_IO_THREAD_TIMEOUT = 4011
ER_STARTING_REPLICA_MONITOR_IO_THREAD = 4012
ER_CANT_USE_ANONYMOUS_TO_GTID_WITH_GTID_MODE_NOT_ON = 4013
ER_CANT_COMBINE_ANONYMOUS_TO_GTID_AND_AUTOPOSITION = 4014
ER_ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_REQUIRES_GTID_MODE_ON = 4015
ER_SQL_REPLICA_SKIP_COUNTER_USED_WITH_GTID_MODE_ON = 4016
ER_USING_ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_AS_LOCAL_OR_UUID = 4017
ER_CANT_SET_ANONYMOUS_TO_GTID_AND_WAIT_UNTIL_SQL_THD_AFTER_GTIDS = 4018
ER_CANT_SET_SQL_AFTER_OR_BEFORE_GTIDS_WITH_ANONYMOUS_TO_GTID = 4019
ER_ANONYMOUS_TO_GTID_UUID_SAME_AS_GROUP_NAME = 4020
ER_CANT_USE_SAME_UUID_AS_GROUP_NAME = 4021
ER_GRP_RPL_RECOVERY_CHANNEL_STILL_RUNNING = 4022
ER_INNODB_INVALID_AUTOEXTEND_SIZE_VALUE = 4023
ER_INNODB_INCOMPATIBLE_WITH_TABLESPACE = 4024
ER_INNODB_AUTOEXTEND_SIZE_OUT_OF_RANGE = 4025
ER_CANNOT_USE_AUTOEXTEND_SIZE_CLAUSE = 4026
ER_ROLE_GRANTED_TO_ITSELF = 4027
ER_TABLE_MUST_HAVE_A_VISIBLE_COLUMN = 4028
ER_INNODB_COMPRESSION_FAILURE = 4029
ER_WARN_ASYNC_CONN_FAILOVER_NETWORK_NAMESPACE = 4030
ER_CLIENT_INTERACTION_TIMEOUT = 4031
ER_INVALID_CAST_TO_GEOMETRY = 4032
ER_INVALID_CAST_POLYGON_RING_DIRECTION = 4033
ER_GIS_DIFFERENT_SRIDS_AGGREGATION = 4034
ER_RELOAD_KEYRING_FAILURE = 4035
ER_SDI_GET_KEYS_INVALID_TABLESPACE = 4036
ER_CHANGE_RPL_SRC_WRONG_COMPRESSION_ALGORITHM_SIZE = 4037
ER_WARN_DEPRECATED_TLS_VERSION_FOR_CHANNEL_CLI = 4038
ER_CANT_USE_SAME_UUID_AS_VIEW_CHANGE_UUID = 4039
ER_ANONYMOUS_TO_GTID_UUID_SAME_AS_VIEW_CHANGE_UUID = 4040
ER_GRP_RPL_VIEW_CHANGE_UUID_FAIL_GET_VARIABLE = 4041
ER_WARN_ADUIT_LOG_MAX_SIZE_AND_PRUNE_SECONDS = 4042
ER_WARN_ADUIT_LOG_MAX_SIZE_CLOSE_TO_ROTATE_ON_SIZE = 4043
ER_KERBEROS_CREATE_USER = 4044
ER_INSTALL_PLUGIN_CONFLICT_CLIENT = 4045
ER_DA_ERROR_LOG_COMPONENT_FLUSH_FAILED = 4046
ER_WARN_SQL_AFTER_MTS_GAPS_GAP_NOT_CALCULATED = 4047
ER_INVALID_ASSIGNMENT_TARGET = 4048
ER_OPERATION_NOT_ALLOWED_ON_GR_SECONDARY = 4049
ER_GRP_RPL_FAILOVER_CHANNEL_STATUS_PROPAGATION = 4050
ER_WARN_AUDIT_LOG_FORMAT_UNIX_TIMESTAMP_ONLY_WHEN_JSON = 4051
ER_INVALID_MFA_PLUGIN_SPECIFIED = 4052
ER_IDENTIFIED_BY_UNSUPPORTED = 4053
ER_INVALID_PLUGIN_FOR_REGISTRATION = 4054
ER_PLUGIN_REQUIRES_REGISTRATION = 4055
ER_MFA_METHOD_EXISTS = 4056
ER_MFA_METHOD_NOT_EXISTS = 4057
ER_AUTHENTICATION_POLICY_MISMATCH = 4058
ER_PLUGIN_REGISTRATION_DONE = 4059
ER_INVALID_USER_FOR_REGISTRATION = 4060
ER_USER_REGISTRATION_FAILED = 4061
ER_MFA_METHODS_INVALID_ORDER = 4062
ER_MFA_METHODS_IDENTICAL = 4063
ER_INVALID_MFA_OPERATIONS_FOR_PASSWORDLESS_USER = 4064
ER_CHANGE_REPLICATION_SOURCE_NO_OPTIONS_FOR_GTID_ONLY = 4065
ER_CHANGE_REP_SOURCE_CANT_DISABLE_REQ_ROW_FORMAT_WITH_GTID_ONLY = 4066
ER_CHANGE_REP_SOURCE_CANT_DISABLE_AUTO_POSITION_WITH_GTID_ONLY = 4067
ER_CHANGE_REP_SOURCE_CANT_DISABLE_GTID_ONLY_WITHOUT_POSITIONS = 4068
ER_CHANGE_REP_SOURCE_CANT_DISABLE_AUTO_POS_WITHOUT_POSITIONS = 4069
ER_CHANGE_REP_SOURCE_GR_CHANNEL_WITH_GTID_MODE_NOT_ON = 4070
ER_CANT_USE_GTID_ONLY_WITH_GTID_MODE_NOT_ON = 4071
ER_WARN_C_DISABLE_GTID_ONLY_WITH_SOURCE_AUTO_POS_INVALID_POS = 4072
ER_DA_SSL_FIPS_MODE_ERROR = 4073
CR_UNKNOWN_ERROR = 2000
CR_SOCKET_CREATE_ERROR = 2001
CR_CONNECTION_ERROR = 2002
CR_CONN_HOST_ERROR = 2003
CR_IPSOCK_ERROR = 2004
CR_UNKNOWN_HOST = 2005
CR_SERVER_GONE_ERROR = 2006
CR_VERSION_ERROR = 2007
CR_OUT_OF_MEMORY = 2008
CR_WRONG_HOST_INFO = 2009
CR_LOCALHOST_CONNECTION = 2010
CR_TCP_CONNECTION = 2011
CR_SERVER_HANDSHAKE_ERR = 2012
CR_SERVER_LOST = 2013
CR_COMMANDS_OUT_OF_SYNC = 2014
CR_NAMEDPIPE_CONNECTION = 2015
CR_NAMEDPIPEWAIT_ERROR = 2016
CR_NAMEDPIPEOPEN_ERROR = 2017
CR_NAMEDPIPESETSTATE_ERROR = 2018
CR_CANT_READ_CHARSET = 2019
CR_NET_PACKET_TOO_LARGE = 2020
CR_EMBEDDED_CONNECTION = 2021
CR_PROBE_SLAVE_STATUS = 2022
CR_PROBE_SLAVE_HOSTS = 2023
CR_PROBE_SLAVE_CONNECT = 2024
CR_PROBE_MASTER_CONNECT = 2025
CR_SSL_CONNECTION_ERROR = 2026
CR_MALFORMED_PACKET = 2027
CR_WRONG_LICENSE = 2028
CR_NULL_POINTER = 2029
CR_NO_PREPARE_STMT = 2030
CR_PARAMS_NOT_BOUND = 2031
CR_DATA_TRUNCATED = 2032
CR_NO_PARAMETERS_EXISTS = 2033
CR_INVALID_PARAMETER_NO = 2034
CR_INVALID_BUFFER_USE = 2035
CR_UNSUPPORTED_PARAM_TYPE = 2036
CR_SHARED_MEMORY_CONNECTION = 2037
CR_SHARED_MEMORY_CONNECT_REQUEST_ERROR = 2038
CR_SHARED_MEMORY_CONNECT_ANSWER_ERROR = 2039
CR_SHARED_MEMORY_CONNECT_FILE_MAP_ERROR = 2040
CR_SHARED_MEMORY_CONNECT_MAP_ERROR = 2041
CR_SHARED_MEMORY_FILE_MAP_ERROR = 2042
CR_SHARED_MEMORY_MAP_ERROR = 2043
CR_SHARED_MEMORY_EVENT_ERROR = 2044
CR_SHARED_MEMORY_CONNECT_ABANDONED_ERROR = 2045
CR_SHARED_MEMORY_CONNECT_SET_ERROR = 2046
CR_CONN_UNKNOW_PROTOCOL = 2047
CR_INVALID_CONN_HANDLE = 2048
CR_UNUSED_1 = 2049
CR_FETCH_CANCELED = 2050
CR_NO_DATA = 2051
CR_NO_STMT_METADATA = 2052
CR_NO_RESULT_SET = 2053
CR_NOT_IMPLEMENTED = 2054
CR_SERVER_LOST_EXTENDED = 2055
CR_STMT_CLOSED = 2056
CR_NEW_STMT_METADATA = 2057
CR_ALREADY_CONNECTED = 2058
CR_AUTH_PLUGIN_CANNOT_LOAD = 2059
CR_DUPLICATE_CONNECTION_ATTR = 2060
CR_AUTH_PLUGIN_ERR = 2061
CR_INSECURE_API_ERR = 2062
CR_FILE_NAME_TOO_LONG = 2063
CR_SSL_FIPS_MODE_ERR = 2064
CR_DEPRECATED_COMPRESSION_NOT_SUPPORTED = 2065
CR_COMPRESSION_WRONGLY_CONFIGURED = 2066
CR_KERBEROS_USER_NOT_FOUND = 2067
CR_LOAD_DATA_LOCAL_INFILE_REJECTED = 2068
CR_LOAD_DATA_LOCAL_INFILE_REALPATH_FAIL = 2069
CR_DNS_SRV_LOOKUP_FAILED = 2070
CR_MANDATORY_TRACKER_NOT_FOUND = 2071
CR_INVALID_FACTOR_NO = 2072
# End MySQL Errors

# Start X Plugin Errors
ER_X_BAD_MESSAGE = 5000
ER_X_CAPABILITIES_PREPARE_FAILED = 5001
ER_X_CAPABILITY_NOT_FOUND = 5002
ER_X_INVALID_PROTOCOL_DATA = 5003
ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_VALUE_LENGTH = 5004
ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_KEY_LENGTH = 5005
ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_EMPTY_KEY = 5006
ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_LENGTH = 5007
ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_TYPE = 5008
ER_X_CAPABILITY_SET_NOT_ALLOWED = 5009
ER_X_SERVICE_ERROR = 5010
ER_X_SESSION = 5011
ER_X_INVALID_ARGUMENT = 5012
ER_X_MISSING_ARGUMENT = 5013
ER_X_BAD_INSERT_DATA = 5014
ER_X_CMD_NUM_ARGUMENTS = 5015
ER_X_CMD_ARGUMENT_TYPE = 5016
ER_X_CMD_ARGUMENT_VALUE = 5017
ER_X_BAD_UPSERT_DATA = 5018
ER_X_DUPLICATED_CAPABILITIES = 5019
ER_X_CMD_ARGUMENT_OBJECT_EMPTY = 5020
ER_X_CMD_INVALID_ARGUMENT = 5021
ER_X_BAD_UPDATE_DATA = 5050
ER_X_BAD_TYPE_OF_UPDATE = 5051
ER_X_BAD_COLUMN_TO_UPDATE = 5052
ER_X_BAD_MEMBER_TO_UPDATE = 5053
ER_X_BAD_STATEMENT_ID = 5110
ER_X_BAD_CURSOR_ID = 5111
ER_X_BAD_SCHEMA = 5112
ER_X_BAD_TABLE = 5113
ER_X_BAD_PROJECTION = 5114
ER_X_DOC_ID_MISSING = 5115
ER_X_DUPLICATE_ENTRY = 5116
ER_X_DOC_REQUIRED_FIELD_MISSING = 5117
ER_X_PROJ_BAD_KEY_NAME = 5120
ER_X_BAD_DOC_PATH = 5121
ER_X_CURSOR_EXISTS = 5122
ER_X_CURSOR_REACHED_EOF = 5123
ER_X_PREPARED_STATMENT_CAN_HAVE_ONE_CURSOR = 5131
ER_X_PREPARED_EXECUTE_ARGUMENT_NOT_SUPPORTED = 5133
ER_X_PREPARED_EXECUTE_ARGUMENT_CONSISTENCY = 5134
ER_X_EXPR_BAD_OPERATOR = 5150
ER_X_EXPR_BAD_NUM_ARGS = 5151
ER_X_EXPR_MISSING_ARG = 5152
ER_X_EXPR_BAD_TYPE_VALUE = 5153
ER_X_EXPR_BAD_VALUE = 5154
ER_X_INVALID_COLLECTION = 5156
ER_X_INVALID_ADMIN_COMMAND = 5157
ER_X_EXPECT_NOT_OPEN = 5158
ER_X_EXPECT_NO_ERROR_FAILED = 5159
ER_X_EXPECT_BAD_CONDITION = 5160
ER_X_EXPECT_BAD_CONDITION_VALUE = 5161
ER_X_INVALID_NAMESPACE = 5162
ER_X_BAD_NOTICE = 5163
ER_X_CANNOT_DISABLE_NOTICE = 5164
ER_X_BAD_CONFIGURATION = 5165
ER_X_MYSQLX_ACCOUNT_MISSING_PERMISSIONS = 5167
ER_X_EXPECT_FIELD_EXISTS_FAILED = 5168
ER_X_BAD_LOCKING = 5169
ER_X_FRAME_COMPRESSION_DISABLED = 5170
ER_X_DECOMPRESSION_FAILED = 5171
ER_X_BAD_COMPRESSED_FRAME = 5174
ER_X_CAPABILITY_COMPRESSION_INVALID_ALGORITHM = 5175
ER_X_CAPABILITY_COMPRESSION_INVALID_SERVER_STYLE = 5176
ER_X_CAPABILITY_COMPRESSION_INVALID_CLIENT_STYLE = 5177
ER_X_CAPABILITY_COMPRESSION_INVALID_OPTION = 5178
ER_X_CAPABILITY_COMPRESSION_MISSING_REQUIRED_FIELDS = 5179
ER_X_DOCUMENT_DOESNT_MATCH_EXPECTED_SCHEMA = 5180
ER_X_COLLECTION_OPTION_DOESNT_EXISTS = 5181
ER_X_INVALID_VALIDATION_SCHEMA = 5182
# End X Plugin Errors
