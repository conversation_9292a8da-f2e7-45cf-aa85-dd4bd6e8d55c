{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useNavigate } from \"react-router-dom\";\nimport { useState } from \"react\";\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AdminDashboard() {\n  _s();\n  const nav = useNavigate();\n  const [processing, setProcessing] = useState(false);\n  const runPipeline = async () => {\n    if (!window.confirm(\"Have ALL uploads finished? This will start the processing pipeline.\")) return;\n    setProcessing(true);\n    try {\n      const token = localStorage.getItem(\"access\");\n      await axios.post(\"/api/tickets/process_uploads/\", {}, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      alert(\"Pipeline Completed\");\n    } catch (err) {\n      var _err$response, _err$response$data;\n      alert(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || \"Error starting process.\");\n    } finally {\n      setProcessing(false);\n    }\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"userData\");\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    nav(\"/auth\", {\n      replace: true\n    });\n    setTimeout(() => {\n      if (window.location.pathname !== \"/auth\") {\n        window.location.href = \"/auth\";\n      }\n    }, 300);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: \"100vh\",\n      backgroundColor: \"#1e1e2f\",\n      // deep slate blue\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      padding: \"2rem\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#2a2a40\",\n        // elegant dark card\n        borderRadius: \"12px\",\n        padding: \"32px\",\n        width: \"100%\",\n        maxWidth: \"480px\",\n        boxShadow: \"0 6px 24px rgba(0, 0, 0, 0.3)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: \"1.75rem\",\n          fontWeight: \"600\",\n          color: \"#f3f4f6\",\n          textAlign: \"center\",\n          marginBottom: \"24px\",\n          borderBottom: \"1px solid #3b3b55\",\n          paddingBottom: \"12px\"\n        },\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"16px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: \"Uploads\",\n          color: \"#3b82f6\",\n          hover: \"#2563eb\",\n          onClick: () => nav(\"/uploads\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: \"Prompt Templates\",\n          color: \"#10b981\",\n          hover: \"#059669\",\n          onClick: () => nav(\"/prompt-manager\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: \"Escalated Tickets\",\n          color: \"#f59e0b\",\n          hover: \"#d97706\",\n          onClick: () => nav(\"/admin/tickets\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: \"Admin Chatbot\",\n          color: \"#8b5cf6\",\n          hover: \"#7c3aed\",\n          onClick: () => nav(\"/admin/chatbot\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: processing ? \"Processing...\" : \"Process Uploads\",\n          color: processing ? \"#6b7280\" : \"#ef4444\",\n          hover: processing ? \"#6b7280\" : \"#dc2626\",\n          onClick: runPipeline,\n          disabled: processing\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n          label: \"Logout\",\n          color: \"#374151\",\n          hover: \"#1f2937\",\n          onClick: handleLogout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminDashboard, \"g9nGB5BTV96G9K3ZLPmmktUDOUI=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nfunction DashboardButton({\n  label,\n  color,\n  hover,\n  onClick,\n  disabled = false\n}) {\n  _s2();\n  const [isHovered, setHovered] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    disabled: disabled,\n    onMouseEnter: () => setHovered(true),\n    onMouseLeave: () => setHovered(false),\n    style: {\n      padding: \"12px 16px\",\n      borderRadius: \"8px\",\n      backgroundColor: isHovered ? hover : color,\n      color: \"#f9fafb\",\n      fontSize: \"1rem\",\n      fontWeight: 600,\n      border: \"none\",\n      cursor: disabled ? \"not-allowed\" : \"pointer\",\n      transition: \"background-color 0.2s ease-in-out\"\n    },\n    children: label\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s2(DashboardButton, \"1/WgnTKFzToT2ZuGk9bruQ0I+z8=\");\n_c2 = DashboardButton;\nvar _c, _c2;\n$RefreshReg$(_c, \"AdminDashboard\");\n$RefreshReg$(_c2, \"DashboardButton\");", "map": {"version": 3, "names": ["useNavigate", "useState", "axios", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "nav", "processing", "setProcessing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "confirm", "token", "localStorage", "getItem", "post", "headers", "Authorization", "alert", "err", "_err$response", "_err$response$data", "response", "data", "detail", "handleLogout", "removeItem", "replace", "setTimeout", "location", "pathname", "href", "style", "minHeight", "backgroundColor", "display", "justifyContent", "alignItems", "padding", "children", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "fontSize", "fontWeight", "color", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "gap", "DashboardButton", "label", "hover", "onClick", "disabled", "_c", "_s2", "isHovered", "setHovered", "onMouseEnter", "onMouseLeave", "border", "cursor", "transition", "_c2", "$RefreshReg$"], "sources": ["D:/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/AdminDashboard.jsx"], "sourcesContent": ["import { useNavigate } from \"react-router-dom\";\r\nimport { useState } from \"react\";\r\nimport axios from \"axios\";\r\n\r\nexport default function AdminDashboard() {\r\n  const nav = useNavigate();\r\n  const [processing, setProcessing] = useState(false);\r\n\r\n  const runPipeline = async () => {\r\n    if (!window.confirm(\"Have ALL uploads finished? This will start the processing pipeline.\")) return;\r\n    setProcessing(true);\r\n    try {\r\n      const token = localStorage.getItem(\"access\");\r\n      await axios.post(\r\n        \"/api/tickets/process_uploads/\",\r\n        {},\r\n        {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }\r\n      );\r\n      alert(\"Pipeline Completed\");\r\n    } catch (err) {\r\n      alert(err.response?.data?.detail || \"Error starting process.\");\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    localStorage.removeItem(\"userData\");\r\n    localStorage.removeItem(\"access\");\r\n    localStorage.removeItem(\"refresh\");\r\n\r\n    nav(\"/auth\", { replace: true });\r\n\r\n    setTimeout(() => {\r\n      if (window.location.pathname !== \"/auth\") {\r\n        window.location.href = \"/auth\";\r\n      }\r\n    }, 300);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        minHeight: \"100vh\",\r\n        backgroundColor: \"#1e1e2f\", // deep slate blue\r\n        display: \"flex\",\r\n        justifyContent: \"center\",\r\n        alignItems: \"center\",\r\n        padding: \"2rem\",\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          backgroundColor: \"#2a2a40\", // elegant dark card\r\n          borderRadius: \"12px\",\r\n          padding: \"32px\",\r\n          width: \"100%\",\r\n          maxWidth: \"480px\",\r\n          boxShadow: \"0 6px 24px rgba(0, 0, 0, 0.3)\",\r\n        }}\r\n      >\r\n        <h2\r\n          style={{\r\n            fontSize: \"1.75rem\",\r\n            fontWeight: \"600\",\r\n            color: \"#f3f4f6\",\r\n            textAlign: \"center\",\r\n            marginBottom: \"24px\",\r\n            borderBottom: \"1px solid #3b3b55\",\r\n            paddingBottom: \"12px\",\r\n          }}\r\n        >\r\n          Admin Dashboard\r\n        </h2>\r\n\r\n        <div style={{ display: \"flex\", flexDirection: \"column\", gap: \"16px\" }}>\r\n          <DashboardButton\r\n            label=\"Uploads\"\r\n            color=\"#3b82f6\"\r\n            hover=\"#2563eb\"\r\n            onClick={() => nav(\"/uploads\")}\r\n          />\r\n          <DashboardButton\r\n            label=\"Prompt Templates\"\r\n            color=\"#10b981\"\r\n            hover=\"#059669\"\r\n            onClick={() => nav(\"/prompt-manager\")}\r\n          />\r\n          <DashboardButton\r\n            label=\"Escalated Tickets\"\r\n            color=\"#f59e0b\"\r\n            hover=\"#d97706\"\r\n            onClick={() => nav(\"/admin/tickets\")}\r\n          />\r\n          <DashboardButton\r\n            label=\"Admin Chatbot\"\r\n            color=\"#8b5cf6\"\r\n            hover=\"#7c3aed\"\r\n            onClick={() => nav(\"/admin/chatbot\")}\r\n          />\r\n          <DashboardButton\r\n            label={processing ? \"Processing...\" : \"Process Uploads\"}\r\n            color={processing ? \"#6b7280\" : \"#ef4444\"}\r\n            hover={processing ? \"#6b7280\" : \"#dc2626\"}\r\n            onClick={runPipeline}\r\n            disabled={processing}\r\n          />\r\n          <DashboardButton\r\n            label=\"Logout\"\r\n            color=\"#374151\"\r\n            hover=\"#1f2937\"\r\n            onClick={handleLogout}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction DashboardButton({ label, color, hover, onClick, disabled = false }) {\r\n  const [isHovered, setHovered] = useState(false);\r\n\r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      disabled={disabled}\r\n      onMouseEnter={() => setHovered(true)}\r\n      onMouseLeave={() => setHovered(false)}\r\n      style={{\r\n        padding: \"12px 16px\",\r\n        borderRadius: \"8px\",\r\n        backgroundColor: isHovered ? hover : color,\r\n        color: \"#f9fafb\",\r\n        fontSize: \"1rem\",\r\n        fontWeight: 600,\r\n        border: \"none\",\r\n        cursor: disabled ? \"not-allowed\" : \"pointer\",\r\n        transition: \"background-color 0.2s ease-in-out\",\r\n      }}\r\n    >\r\n      {label}\r\n    </button>\r\n  );\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAMC,GAAG,GAAGP,WAAW,CAAC,CAAC;EACzB,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,qEAAqE,CAAC,EAAE;IAC5FH,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC5C,MAAMb,KAAK,CAACc,IAAI,CACd,+BAA+B,EAC/B,CAAC,CAAC,EACF;QACEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUL,KAAK;QAAG;MAC9C,CACF,CAAC;MACDM,KAAK,CAAC,oBAAoB,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZH,KAAK,CAAC,EAAAE,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAI,yBAAyB,CAAC;IAChE,CAAC,SAAS;MACRhB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzBZ,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;IACnCb,YAAY,CAACa,UAAU,CAAC,QAAQ,CAAC;IACjCb,YAAY,CAACa,UAAU,CAAC,SAAS,CAAC;IAElCpB,GAAG,CAAC,OAAO,EAAE;MAAEqB,OAAO,EAAE;IAAK,CAAC,CAAC;IAE/BC,UAAU,CAAC,MAAM;MACf,IAAIlB,MAAM,CAACmB,QAAQ,CAACC,QAAQ,KAAK,OAAO,EAAE;QACxCpB,MAAM,CAACmB,QAAQ,CAACE,IAAI,GAAG,OAAO;MAChC;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,oBACE5B,OAAA;IACE6B,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE,SAAS;MAAE;MAC5BC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eAEFpC,OAAA;MACE6B,KAAK,EAAE;QACLE,eAAe,EAAE,SAAS;QAAE;QAC5BM,YAAY,EAAE,MAAM;QACpBF,OAAO,EAAE,MAAM;QACfG,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAJ,QAAA,gBAEFpC,OAAA;QACE6B,KAAK,EAAE;UACLY,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,QAAQ;UACnBC,YAAY,EAAE,MAAM;UACpBC,YAAY,EAAE,mBAAmB;UACjCC,aAAa,EAAE;QACjB,CAAE;QAAAX,QAAA,EACH;MAED;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnD,OAAA;QAAK6B,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEoB,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAjB,QAAA,gBACpEpC,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAC,SAAS;UACfZ,KAAK,EAAC,SAAS;UACfa,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMtD,GAAG,CAAC,UAAU;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACFnD,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAC,kBAAkB;UACxBZ,KAAK,EAAC,SAAS;UACfa,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMtD,GAAG,CAAC,iBAAiB;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACFnD,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAC,mBAAmB;UACzBZ,KAAK,EAAC,SAAS;UACfa,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMtD,GAAG,CAAC,gBAAgB;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACFnD,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAC,eAAe;UACrBZ,KAAK,EAAC,SAAS;UACfa,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMtD,GAAG,CAAC,gBAAgB;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACFnD,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAEnD,UAAU,GAAG,eAAe,GAAG,iBAAkB;UACxDuC,KAAK,EAAEvC,UAAU,GAAG,SAAS,GAAG,SAAU;UAC1CoD,KAAK,EAAEpD,UAAU,GAAG,SAAS,GAAG,SAAU;UAC1CqD,OAAO,EAAEnD,WAAY;UACrBoD,QAAQ,EAAEtD;QAAW;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACFnD,OAAA,CAACsD,eAAe;UACdC,KAAK,EAAC,QAAQ;UACdZ,KAAK,EAAC,SAAS;UACfa,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEnC;QAAa;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjD,EAAA,CAnHuBD,cAAc;EAAA,QACxBL,WAAW;AAAA;AAAA+D,EAAA,GADD1D,cAAc;AAqHtC,SAASqD,eAAeA,CAAC;EAAEC,KAAK;EAAEZ,KAAK;EAAEa,KAAK;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,EAAE;EAAAE,GAAA;EAC3E,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAE/C,oBACEG,OAAA;IACEyD,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IACnBK,YAAY,EAAEA,CAAA,KAAMD,UAAU,CAAC,IAAI,CAAE;IACrCE,YAAY,EAAEA,CAAA,KAAMF,UAAU,CAAC,KAAK,CAAE;IACtCjC,KAAK,EAAE;MACLM,OAAO,EAAE,WAAW;MACpBE,YAAY,EAAE,KAAK;MACnBN,eAAe,EAAE8B,SAAS,GAAGL,KAAK,GAAGb,KAAK;MAC1CA,KAAK,EAAE,SAAS;MAChBF,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,GAAG;MACfuB,MAAM,EAAE,MAAM;MACdC,MAAM,EAAER,QAAQ,GAAG,aAAa,GAAG,SAAS;MAC5CS,UAAU,EAAE;IACd,CAAE;IAAA/B,QAAA,EAEDmB;EAAK;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACS,GAAA,CAxBQN,eAAe;AAAAc,GAAA,GAAfd,eAAe;AAAA,IAAAK,EAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}