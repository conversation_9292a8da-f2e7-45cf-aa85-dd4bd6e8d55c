[{"D:\\AI-Agent-Chatbot-main\\frontend\\src\\index.js": "1", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\App.jsx": "2", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminPromptPage.jsx": "3", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminDashboard.jsx": "4", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\upload.jsx": "5", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminChatbot.jsx": "6", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\Home.jsx": "7", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AuthForm.jsx": "8", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\NewTicketForm.jsx": "9", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\ActionsPage.jsx": "10", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\EscalatedTicketsPage.jsx": "11", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\PendingTicketsList.jsx": "12", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\StructuredChatbot.jsx": "13", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\SelectTicketPage.jsx": "14", "D:\\AI-Agent-Chatbot-main\\frontend\\src\\YesNoButtons.jsx": "15"}, {"size": 246, "mtime": 1751393046000, "results": "16", "hashOfConfig": "17"}, {"size": 6215, "mtime": 1751571842000, "results": "18", "hashOfConfig": "17"}, {"size": 6245, "mtime": 1751651140480, "results": "19", "hashOfConfig": "17"}, {"size": 4112, "mtime": 1751492714000, "results": "20", "hashOfConfig": "17"}, {"size": 2696, "mtime": 1751651170486, "results": "21", "hashOfConfig": "17"}, {"size": 8821, "mtime": 1751061996000, "results": "22", "hashOfConfig": "17"}, {"size": 56977, "mtime": 1751576266000, "results": "23", "hashOfConfig": "17"}, {"size": 8278, "mtime": 1751012174000, "results": "24", "hashOfConfig": "17"}, {"size": 10343, "mtime": 1751650031966, "results": "25", "hashOfConfig": "17"}, {"size": 3744, "mtime": 1751571764000, "results": "26", "hashOfConfig": "17"}, {"size": 1910, "mtime": 1751010770000, "results": "27", "hashOfConfig": "17"}, {"size": 11360, "mtime": 1751571804000, "results": "28", "hashOfConfig": "17"}, {"size": 13790, "mtime": 1751566714000, "results": "29", "hashOfConfig": "17"}, {"size": 7171, "mtime": 1751566570000, "results": "30", "hashOfConfig": "17"}, {"size": 6849, "mtime": 1751566604000, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1i2d3r4", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\AI-Agent-Chatbot-main\\frontend\\src\\index.js", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\App.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminPromptPage.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminDashboard.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\upload.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AdminChatbot.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\Home.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\AuthForm.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\NewTicketForm.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\ActionsPage.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\EscalatedTicketsPage.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\PendingTicketsList.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\StructuredChatbot.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\SelectTicketPage.jsx", [], [], "D:\\AI-Agent-Chatbot-main\\frontend\\src\\YesNoButtons.jsx", [], []]