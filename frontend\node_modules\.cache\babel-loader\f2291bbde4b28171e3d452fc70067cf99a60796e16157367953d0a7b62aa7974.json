{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\StructuredChatbot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = \"http://localhost:8000\";\nexport default function StructuredChatbot({\n  token\n}) {\n  _s();\n  const {\n    ticketId\n  } = useParams(); // Can be ticket number or \"general\"\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const messagesEndRef = useRef(null);\n\n  // States\n  const [messages, setMessages] = useState([]);\n  const [query, setQuery] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [sessionLoading, setSessionLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [sessionData, setSessionData] = useState(null);\n  const [awaitingResponse, setAwaitingResponse] = useState(false);\n  const [responseType, setResponseType] = useState(\"\"); // \"close_ticket\", \"more_queries\", \"file_download\", etc.\n  const [pendingFiles, setPendingFiles] = useState([]);\n\n  // Auto-scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages, loading]);\n\n  // Initialize session\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n    initializeSession();\n  }, [accessToken, ticketId, navigate]);\n  const initializeSession = async () => {\n    setSessionLoading(true);\n    try {\n      let endpoint;\n      if (ticketId === \"general\") {\n        endpoint = `${BACKEND_URL}/api/start_general_session/`;\n      } else {\n        endpoint = `${BACKEND_URL}/api/start_ticket_session/${ticketId}/`;\n      }\n      const response = await fetch(endpoint, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setSessionData(data);\n        addBotMessage(data.initial_message);\n      } else {\n        setError(data.error || \"Failed to start session\");\n        setTimeout(() => navigate(\"/actions\"), 3000);\n      }\n    } catch (err) {\n      console.error(\"Session initialization error:\", err);\n      setError(\"Network error. Redirecting...\");\n      setTimeout(() => navigate(\"/actions\"), 3000);\n    } finally {\n      setSessionLoading(false);\n    }\n  };\n  const addBotMessage = (content, showButtons = false, buttonType = \"\") => {\n    const newMessage = {\n      id: Date.now(),\n      type: \"bot\",\n      content,\n      timestamp: new Date(),\n      showButtons,\n      buttonType\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n  const addUserMessage = content => {\n    const newMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n  const handleSendMessage = async () => {\n    if (!query.trim() || loading || awaitingResponse) return;\n    const userQuery = query.trim();\n    setQuery(\"\");\n    addUserMessage(userQuery);\n    setLoading(true);\n    setError(\"\");\n    try {\n      var _sessionData$ticket;\n      const requestBody = {\n        query: userQuery,\n        ticket_mode: (sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\",\n        ticket_id: (sessionData === null || sessionData === void 0 ? void 0 : (_sessionData$ticket = sessionData.ticket) === null || _sessionData$ticket === void 0 ? void 0 : _sessionData$ticket.ticket_number) || null\n      };\n\n      // Handle problem description for new tickets\n      if (sessionData !== null && sessionData !== void 0 && sessionData.can_add_problem && (sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\") {\n        const response = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          },\n          body: JSON.stringify({\n            ticket_number: sessionData.ticket.ticket_number,\n            problem_description: userQuery\n          })\n        });\n        const data = await response.json();\n        if (response.ok) {\n          addBotMessage(data.answer);\n          if (data.files && data.files.length > 0) {\n            setPendingFiles(data.files);\n            addBotMessage(\"💡 I found relevant documentation. Would you like me to provide the file for detailed information?\", true, \"file_download\");\n            setAwaitingResponse(true);\n            setResponseType(\"file_download\");\n          } else {\n            addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n            setAwaitingResponse(true);\n            setResponseType(\"more_queries\");\n          }\n          // Update session data to reflect that problem description is added\n          setSessionData(prev => ({\n            ...prev,\n            can_add_problem: false\n          }));\n        } else {\n          addBotMessage(\"❌ \" + (data.error || \"Failed to process your request.\"));\n        }\n      } else {\n        // Regular chat\n        const response = await fetch(`${BACKEND_URL}/api/chat/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        const data = await response.json();\n        if (response.ok) {\n          addBotMessage(data.answer);\n          if (data.files && data.files.length > 0) {\n            setPendingFiles(data.files);\n            addBotMessage(\"💡 I found relevant documentation. Would you like me to provide the file for detailed information?\", true, \"file_download\");\n            setAwaitingResponse(true);\n            setResponseType(\"file_download\");\n          } else if ((sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\") {\n            addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n            setAwaitingResponse(true);\n            setResponseType(\"more_queries\");\n          }\n        } else {\n          addBotMessage(\"❌ \" + (data.error || \"Failed to get response.\"));\n        }\n      }\n    } catch (err) {\n      console.error(\"Chat error:\", err);\n      addBotMessage(\"❌ Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleYesNoResponse = async (response, type) => {\n    setAwaitingResponse(false);\n    addUserMessage(response ? \"Yes\" : \"No\");\n    if (type === \"file_download\") {\n      if (response) {\n        // Download file\n        if (pendingFiles.length > 0) {\n          const file = pendingFiles[0];\n          const fileUrl = `${BACKEND_URL}${file.url}?token=${accessToken}`;\n          window.open(fileUrl, '_blank');\n          addBotMessage(\"📄 File opened in a new tab. After reviewing, do you have any other queries?\", true, \"more_queries\");\n          setResponseType(\"more_queries\");\n          setAwaitingResponse(true);\n        }\n      } else {\n        if ((sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\") {\n          addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n          setResponseType(\"more_queries\");\n          setAwaitingResponse(true);\n        }\n      }\n      setPendingFiles([]);\n    } else if (type === \"more_queries\") {\n      if (response) {\n        addBotMessage(\"Please go ahead and ask your question.\");\n      } else {\n        if ((sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\") {\n          addBotMessage(\"Would you like to close this ticket?\", true, \"close_ticket\");\n          setResponseType(\"close_ticket\");\n          setAwaitingResponse(true);\n        } else {\n          addBotMessage(\"Thank you for using our support system. Have a great day!\");\n          setTimeout(() => handleLogout(), 2000);\n        }\n      }\n    } else if (type === \"close_ticket\") {\n      if (response) {\n        await closeTicket();\n      } else {\n        addBotMessage(\"Ticket will remain open. You can continue asking questions or return later.\");\n      }\n    }\n  };\n  const closeTicket = async () => {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`\n        },\n        body: JSON.stringify({\n          ticket_number: sessionData.ticket.ticket_number,\n          status: \"closed\"\n        })\n      });\n      if (response.ok) {\n        addBotMessage(\"✅ Ticket has been closed successfully. Thank you for using our support system!\");\n        setTimeout(() => handleLogout(), 3000);\n      } else {\n        addBotMessage(\"❌ Failed to close ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error closing ticket:\", err);\n      addBotMessage(\"❌ Network error while closing ticket.\");\n    }\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    localStorage.removeItem(\"userData\");\n    navigate(\"/auth\");\n  };\n  const handleKeyPress = e => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  if (sessionLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"40px\",\n        textAlign: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Starting your session...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u23F3 Please wait...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"40px\",\n        textAlign: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: \"#d32f2f\"\n        },\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(\"/actions\"),\n        style: {\n          marginTop: \"20px\",\n          padding: \"10px 20px\"\n        },\n        children: \"Back to Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      height: \"100vh\",\n      fontFamily: \"Arial, sans-serif\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"15px 20px\",\n        backgroundColor: \"#f5f5f5\",\n        borderBottom: \"1px solid #ddd\",\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: 0,\n          color: \"#333\"\n        },\n        children: (sessionData === null || sessionData === void 0 ? void 0 : sessionData.session_type) === \"ticket\" ? `Ticket #${sessionData.ticket.ticket_number}` : \"General Support Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(\"/actions\"),\n        style: {\n          padding: \"8px 16px\",\n          backgroundColor: \"#6c757d\",\n          color: \"white\",\n          border: \"none\",\n          borderRadius: \"4px\",\n          cursor: \"pointer\"\n        },\n        children: \"\\u2190 Back to Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: \"auto\",\n        padding: \"20px\",\n        backgroundColor: \"#fafafa\"\n      },\n      children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: message.type === \"user\" ? \"flex-end\" : \"flex-start\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: \"70%\",\n              padding: \"12px 16px\",\n              borderRadius: \"12px\",\n              backgroundColor: message.type === \"user\" ? \"#2196F3\" : \"#e0e0e0\",\n              color: message.type === \"user\" ? \"white\" : \"#333\",\n              whiteSpace: \"pre-wrap\"\n            },\n            children: message.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), message.showButtons && message.buttonType && awaitingResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"10px\",\n            textAlign: \"center\"\n          },\n          children: [message.buttonType === \"file_download\" && /*#__PURE__*/_jsxDEV(FileDownloadButtons, {\n            onDownload: () => handleYesNoResponse(true, \"file_download\"),\n            onSkip: () => handleYesNoResponse(false, \"file_download\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 19\n          }, this), (message.buttonType === \"more_queries\" || message.buttonType === \"close_ticket\") && /*#__PURE__*/_jsxDEV(YesNoButtons, {\n            onYes: () => handleYesNoResponse(true, message.buttonType),\n            onNo: () => handleYesNoResponse(false, message.buttonType)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 15\n        }, this)]\n      }, message.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\",\n          color: \"#666\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83E\\uDD16 Thinking...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"20px\",\n        backgroundColor: \"white\",\n        borderTop: \"1px solid #ddd\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          gap: \"10px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: query,\n          onChange: e => setQuery(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: awaitingResponse ? \"Please use the buttons above to respond...\" : \"Type your message...\",\n          disabled: loading || awaitingResponse,\n          style: {\n            flex: 1,\n            padding: \"12px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"6px\",\n            resize: \"none\",\n            minHeight: \"50px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSendMessage,\n          disabled: loading || awaitingResponse || !query.trim(),\n          style: {\n            padding: \"12px 24px\",\n            backgroundColor: loading || awaitingResponse || !query.trim() ? \"#ccc\" : \"#2196F3\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"6px\",\n            cursor: loading || awaitingResponse || !query.trim() ? \"not-allowed\" : \"pointer\",\n            fontSize: \"16px\"\n          },\n          children: \"Send\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n}\n_s(StructuredChatbot, \"P0BG8pE6Zqu5cm/tEkTWWS1krNA=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = StructuredChatbot;\nvar _c;\n$RefreshReg$(_c, \"StructuredChatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useNavigate", "YesNoButtons", "FileDownloadButtons", "TicketCloseButtons", "jsxDEV", "_jsxDEV", "BACKEND_URL", "StructuredChatbot", "token", "_s", "ticketId", "navigate", "accessToken", "localStorage", "getItem", "messagesEndRef", "messages", "setMessages", "query", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "sessionLoading", "setSessionLoading", "error", "setError", "sessionData", "setSessionData", "awaitingResponse", "setAwaitingResponse", "responseType", "setResponseType", "pendingFiles", "setPendingFiles", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "initializeSession", "endpoint", "response", "fetch", "headers", "Authorization", "data", "json", "ok", "addBotMessage", "initial_message", "setTimeout", "err", "console", "content", "showButtons", "buttonType", "newMessage", "id", "Date", "now", "type", "timestamp", "prev", "addUserMessage", "handleSendMessage", "trim", "userQuery", "_sessionData$ticket", "requestBody", "ticket_mode", "session_type", "ticket_id", "ticket", "ticket_number", "can_add_problem", "method", "body", "JSON", "stringify", "problem_description", "answer", "files", "length", "handleYesNoResponse", "file", "fileUrl", "url", "window", "open", "handleLogout", "closeTicket", "status", "removeItem", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "style", "padding", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "marginTop", "display", "flexDirection", "height", "fontFamily", "backgroundColor", "borderBottom", "justifyContent", "alignItems", "margin", "border", "borderRadius", "cursor", "flex", "overflowY", "map", "message", "marginBottom", "max<PERSON><PERSON><PERSON>", "whiteSpace", "onDownload", "onSkip", "onYes", "onNo", "ref", "borderTop", "gap", "value", "onChange", "target", "onKeyPress", "placeholder", "disabled", "resize", "minHeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["D:/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/StructuredChatbot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from \"./YesNoButtons\";\nimport \"./App.css\";\n\nconst BACKEND_URL = \"http://localhost:8000\";\n\nexport default function StructuredChatbot({ token }) {\n  const { ticketId } = useParams(); // Can be ticket number or \"general\"\n  const navigate = useNavigate();\n  const accessToken = token || localStorage.getItem(\"access\");\n  const messagesEndRef = useRef(null);\n\n  // States\n  const [messages, setMessages] = useState([]);\n  const [query, setQuery] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [sessionLoading, setSessionLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [sessionData, setSessionData] = useState(null);\n  const [awaitingResponse, setAwaitingResponse] = useState(false);\n  const [responseType, setResponseType] = useState(\"\"); // \"close_ticket\", \"more_queries\", \"file_download\", etc.\n  const [pendingFiles, setPendingFiles] = useState([]);\n\n  // Auto-scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  }, [messages, loading]);\n\n  // Initialize session\n  useEffect(() => {\n    if (!accessToken) {\n      navigate(\"/auth\");\n      return;\n    }\n\n    initializeSession();\n  }, [accessToken, ticketId, navigate]);\n\n  const initializeSession = async () => {\n    setSessionLoading(true);\n    try {\n      let endpoint;\n      if (ticketId === \"general\") {\n        endpoint = `${BACKEND_URL}/api/start_general_session/`;\n      } else {\n        endpoint = `${BACKEND_URL}/api/start_ticket_session/${ticketId}/`;\n      }\n\n      const response = await fetch(endpoint, {\n        headers: {\n          Authorization: `Bearer ${accessToken}`,\n        },\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setSessionData(data);\n        addBotMessage(data.initial_message);\n      } else {\n        setError(data.error || \"Failed to start session\");\n        setTimeout(() => navigate(\"/actions\"), 3000);\n      }\n    } catch (err) {\n      console.error(\"Session initialization error:\", err);\n      setError(\"Network error. Redirecting...\");\n      setTimeout(() => navigate(\"/actions\"), 3000);\n    } finally {\n      setSessionLoading(false);\n    }\n  };\n\n  const addBotMessage = (content, showButtons = false, buttonType = \"\") => {\n    const newMessage = {\n      id: Date.now(),\n      type: \"bot\",\n      content,\n      timestamp: new Date(),\n      showButtons,\n      buttonType\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n\n  const addUserMessage = (content) => {\n    const newMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n\n  const handleSendMessage = async () => {\n    if (!query.trim() || loading || awaitingResponse) return;\n\n    const userQuery = query.trim();\n    setQuery(\"\");\n    addUserMessage(userQuery);\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const requestBody = {\n        query: userQuery,\n        ticket_mode: sessionData?.session_type === \"ticket\",\n        ticket_id: sessionData?.ticket?.ticket_number || null,\n      };\n\n      // Handle problem description for new tickets\n      if (sessionData?.can_add_problem && sessionData?.session_type === \"ticket\") {\n        const response = await fetch(`${BACKEND_URL}/api/add_problem_description/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n          body: JSON.stringify({\n            ticket_number: sessionData.ticket.ticket_number,\n            problem_description: userQuery\n          }),\n        });\n\n        const data = await response.json();\n\n        if (response.ok) {\n          addBotMessage(data.answer);\n          if (data.files && data.files.length > 0) {\n            setPendingFiles(data.files);\n            addBotMessage(\"💡 I found relevant documentation. Would you like me to provide the file for detailed information?\", true, \"file_download\");\n            setAwaitingResponse(true);\n            setResponseType(\"file_download\");\n          } else {\n            addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n            setAwaitingResponse(true);\n            setResponseType(\"more_queries\");\n          }\n          // Update session data to reflect that problem description is added\n          setSessionData(prev => ({ ...prev, can_add_problem: false }));\n        } else {\n          addBotMessage(\"❌ \" + (data.error || \"Failed to process your request.\"));\n        }\n      } else {\n        // Regular chat\n        const response = await fetch(`${BACKEND_URL}/api/chat/`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n          body: JSON.stringify(requestBody),\n        });\n\n        const data = await response.json();\n\n        if (response.ok) {\n          addBotMessage(data.answer);\n          \n          if (data.files && data.files.length > 0) {\n            setPendingFiles(data.files);\n            addBotMessage(\"💡 I found relevant documentation. Would you like me to provide the file for detailed information?\", true, \"file_download\");\n            setAwaitingResponse(true);\n            setResponseType(\"file_download\");\n          } else if (sessionData?.session_type === \"ticket\") {\n            addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n            setAwaitingResponse(true);\n            setResponseType(\"more_queries\");\n          }\n        } else {\n          addBotMessage(\"❌ \" + (data.error || \"Failed to get response.\"));\n        }\n      }\n    } catch (err) {\n      console.error(\"Chat error:\", err);\n      addBotMessage(\"❌ Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleYesNoResponse = async (response, type) => {\n    setAwaitingResponse(false);\n    addUserMessage(response ? \"Yes\" : \"No\");\n\n    if (type === \"file_download\") {\n      if (response) {\n        // Download file\n        if (pendingFiles.length > 0) {\n          const file = pendingFiles[0];\n          const fileUrl = `${BACKEND_URL}${file.url}?token=${accessToken}`;\n          window.open(fileUrl, '_blank');\n          addBotMessage(\"📄 File opened in a new tab. After reviewing, do you have any other queries?\", true, \"more_queries\");\n          setResponseType(\"more_queries\");\n          setAwaitingResponse(true);\n        }\n      } else {\n        if (sessionData?.session_type === \"ticket\") {\n          addBotMessage(\"🟢 Do you have any other queries about this issue?\", true, \"more_queries\");\n          setResponseType(\"more_queries\");\n          setAwaitingResponse(true);\n        }\n      }\n      setPendingFiles([]);\n    } else if (type === \"more_queries\") {\n      if (response) {\n        addBotMessage(\"Please go ahead and ask your question.\");\n      } else {\n        if (sessionData?.session_type === \"ticket\") {\n          addBotMessage(\"Would you like to close this ticket?\", true, \"close_ticket\");\n          setResponseType(\"close_ticket\");\n          setAwaitingResponse(true);\n        } else {\n          addBotMessage(\"Thank you for using our support system. Have a great day!\");\n          setTimeout(() => handleLogout(), 2000);\n        }\n      }\n    } else if (type === \"close_ticket\") {\n      if (response) {\n        await closeTicket();\n      } else {\n        addBotMessage(\"Ticket will remain open. You can continue asking questions or return later.\");\n      }\n    }\n  };\n\n  const closeTicket = async () => {\n    try {\n      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`,\n        },\n        body: JSON.stringify({\n          ticket_number: sessionData.ticket.ticket_number,\n          status: \"closed\"\n        }),\n      });\n\n      if (response.ok) {\n        addBotMessage(\"✅ Ticket has been closed successfully. Thank you for using our support system!\");\n        setTimeout(() => handleLogout(), 3000);\n      } else {\n        addBotMessage(\"❌ Failed to close ticket. Please try again.\");\n      }\n    } catch (err) {\n      console.error(\"Error closing ticket:\", err);\n      addBotMessage(\"❌ Network error while closing ticket.\");\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"access\");\n    localStorage.removeItem(\"refresh\");\n    localStorage.removeItem(\"userData\");\n    navigate(\"/auth\");\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (sessionLoading) {\n    return (\n      <div style={{ padding: \"40px\", textAlign: \"center\" }}>\n        <h2>Starting your session...</h2>\n        <p>⏳ Please wait...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: \"40px\", textAlign: \"center\" }}>\n        <h2 style={{ color: \"#d32f2f\" }}>Error</h2>\n        <p>{error}</p>\n        <button onClick={() => navigate(\"/actions\")} style={{ marginTop: \"20px\", padding: \"10px 20px\" }}>\n          Back to Actions\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ \n      display: \"flex\", \n      flexDirection: \"column\", \n      height: \"100vh\", \n      fontFamily: \"Arial, sans-serif\" \n    }}>\n      {/* Header */}\n      <div style={{ \n        padding: \"15px 20px\", \n        backgroundColor: \"#f5f5f5\", \n        borderBottom: \"1px solid #ddd\",\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\"\n      }}>\n        <h2 style={{ margin: 0, color: \"#333\" }}>\n          {sessionData?.session_type === \"ticket\" \n            ? `Ticket #${sessionData.ticket.ticket_number}` \n            : \"General Support Chat\"}\n        </h2>\n        <button\n          onClick={() => navigate(\"/actions\")}\n          style={{\n            padding: \"8px 16px\",\n            backgroundColor: \"#6c757d\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            cursor: \"pointer\"\n          }}\n        >\n          ← Back to Actions\n        </button>\n      </div>\n\n      {/* Messages */}\n      <div style={{ \n        flex: 1, \n        overflowY: \"auto\", \n        padding: \"20px\",\n        backgroundColor: \"#fafafa\"\n      }}>\n        {messages.map((message) => (\n          <div key={message.id} style={{ marginBottom: \"15px\" }}>\n            <div style={{\n              display: \"flex\",\n              justifyContent: message.type === \"user\" ? \"flex-end\" : \"flex-start\"\n            }}>\n              <div style={{\n                maxWidth: \"70%\",\n                padding: \"12px 16px\",\n                borderRadius: \"12px\",\n                backgroundColor: message.type === \"user\" ? \"#2196F3\" : \"#e0e0e0\",\n                color: message.type === \"user\" ? \"white\" : \"#333\",\n                whiteSpace: \"pre-wrap\"\n              }}>\n                {message.content}\n              </div>\n            </div>\n            \n            {message.showButtons && message.buttonType && awaitingResponse && (\n              <div style={{ marginTop: \"10px\", textAlign: \"center\" }}>\n                {message.buttonType === \"file_download\" && (\n                  <FileDownloadButtons\n                    onDownload={() => handleYesNoResponse(true, \"file_download\")}\n                    onSkip={() => handleYesNoResponse(false, \"file_download\")}\n                  />\n                )}\n                {(message.buttonType === \"more_queries\" || message.buttonType === \"close_ticket\") && (\n                  <YesNoButtons\n                    onYes={() => handleYesNoResponse(true, message.buttonType)}\n                    onNo={() => handleYesNoResponse(false, message.buttonType)}\n                  />\n                )}\n              </div>\n            )}\n          </div>\n        ))}\n        \n        {loading && (\n          <div style={{ textAlign: \"center\", color: \"#666\" }}>\n            <p>🤖 Thinking...</p>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div style={{ \n        padding: \"20px\", \n        backgroundColor: \"white\", \n        borderTop: \"1px solid #ddd\" \n      }}>\n        <div style={{ display: \"flex\", gap: \"10px\" }}>\n          <textarea\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder={awaitingResponse ? \"Please use the buttons above to respond...\" : \"Type your message...\"}\n            disabled={loading || awaitingResponse}\n            style={{\n              flex: 1,\n              padding: \"12px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"6px\",\n              resize: \"none\",\n              minHeight: \"50px\",\n              fontSize: \"16px\"\n            }}\n          />\n          <button\n            onClick={handleSendMessage}\n            disabled={loading || awaitingResponse || !query.trim()}\n            style={{\n              padding: \"12px 24px\",\n              backgroundColor: loading || awaitingResponse || !query.trim() ? \"#ccc\" : \"#2196F3\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"6px\",\n              cursor: loading || awaitingResponse || !query.trim() ? \"not-allowed\" : \"pointer\",\n              fontSize: \"16px\"\n            }}\n          >\n            Send\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,gBAAgB;AACtF,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,WAAW,GAAG,uBAAuB;AAE3C,eAAe,SAASC,iBAAiBA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACnD,MAAM;IAAEC;EAAS,CAAC,GAAGX,SAAS,CAAC,CAAC,CAAC,CAAC;EAClC,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,WAAW,GAAGJ,KAAK,IAAIK,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC3D,MAAMC,cAAc,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IAAA,IAAAqC,qBAAA;IACd,CAAAA,qBAAA,GAAAnB,cAAc,CAACoB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACrB,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACAvB,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,WAAW,EAAE;MAChBD,QAAQ,CAAC,OAAO,CAAC;MACjB;IACF;IAEA2B,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC1B,WAAW,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAErC,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCf,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI;MACF,IAAIgB,QAAQ;MACZ,IAAI7B,QAAQ,KAAK,SAAS,EAAE;QAC1B6B,QAAQ,GAAG,GAAGjC,WAAW,6BAA6B;MACxD,CAAC,MAAM;QACLiC,QAAQ,GAAG,GAAGjC,WAAW,6BAA6BI,QAAQ,GAAG;MACnE;MAEA,MAAM8B,QAAQ,GAAG,MAAMC,KAAK,CAACF,QAAQ,EAAE;QACrCG,OAAO,EAAE;UACPC,aAAa,EAAE,UAAU/B,WAAW;QACtC;MACF,CAAC,CAAC;MAEF,MAAMgC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACfnB,cAAc,CAACiB,IAAI,CAAC;QACpBG,aAAa,CAACH,IAAI,CAACI,eAAe,CAAC;MACrC,CAAC,MAAM;QACLvB,QAAQ,CAACmB,IAAI,CAACpB,KAAK,IAAI,yBAAyB,CAAC;QACjDyB,UAAU,CAAC,MAAMtC,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,+BAA+B,EAAE0B,GAAG,CAAC;MACnDzB,QAAQ,CAAC,+BAA+B,CAAC;MACzCwB,UAAU,CAAC,MAAMtC,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC;IAC9C,CAAC,SAAS;MACRY,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMwB,aAAa,GAAGA,CAACK,OAAO,EAAEC,WAAW,GAAG,KAAK,EAAEC,UAAU,GAAG,EAAE,KAAK;IACvE,MAAMC,UAAU,GAAG;MACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,KAAK;MACXP,OAAO;MACPQ,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC;MACrBJ,WAAW;MACXC;IACF,CAAC;IACDrC,WAAW,CAAC4C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEN,UAAU,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMO,cAAc,GAAIV,OAAO,IAAK;IAClC,MAAMG,UAAU,GAAG;MACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZP,OAAO;MACPQ,SAAS,EAAE,IAAIH,IAAI,CAAC;IACtB,CAAC;IACDxC,WAAW,CAAC4C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEN,UAAU,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMQ,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC7C,KAAK,CAAC8C,IAAI,CAAC,CAAC,IAAI5C,OAAO,IAAIQ,gBAAgB,EAAE;IAElD,MAAMqC,SAAS,GAAG/C,KAAK,CAAC8C,IAAI,CAAC,CAAC;IAC9B7C,QAAQ,CAAC,EAAE,CAAC;IACZ2C,cAAc,CAACG,SAAS,CAAC;IACzB5C,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MAAA,IAAAyC,mBAAA;MACF,MAAMC,WAAW,GAAG;QAClBjD,KAAK,EAAE+C,SAAS;QAChBG,WAAW,EAAE,CAAA1C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ;QACnDC,SAAS,EAAE,CAAA5C,WAAW,aAAXA,WAAW,wBAAAwC,mBAAA,GAAXxC,WAAW,CAAE6C,MAAM,cAAAL,mBAAA,uBAAnBA,mBAAA,CAAqBM,aAAa,KAAI;MACnD,CAAC;;MAED;MACA,IAAI9C,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE+C,eAAe,IAAI,CAAA/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ,EAAE;QAC1E,MAAM7B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnC,WAAW,+BAA+B,EAAE;UAC1EoE,MAAM,EAAE,MAAM;UACdhC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU/B,WAAW;UACtC,CAAC;UACD+D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBL,aAAa,EAAE9C,WAAW,CAAC6C,MAAM,CAACC,aAAa;YAC/CM,mBAAmB,EAAEb;UACvB,CAAC;QACH,CAAC,CAAC;QAEF,MAAMrB,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;UACfC,aAAa,CAACH,IAAI,CAACmC,MAAM,CAAC;UAC1B,IAAInC,IAAI,CAACoC,KAAK,IAAIpC,IAAI,CAACoC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;YACvChD,eAAe,CAACW,IAAI,CAACoC,KAAK,CAAC;YAC3BjC,aAAa,CAAC,oGAAoG,EAAE,IAAI,EAAE,eAAe,CAAC;YAC1IlB,mBAAmB,CAAC,IAAI,CAAC;YACzBE,eAAe,CAAC,eAAe,CAAC;UAClC,CAAC,MAAM;YACLgB,aAAa,CAAC,oDAAoD,EAAE,IAAI,EAAE,cAAc,CAAC;YACzFlB,mBAAmB,CAAC,IAAI,CAAC;YACzBE,eAAe,CAAC,cAAc,CAAC;UACjC;UACA;UACAJ,cAAc,CAACkC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEY,eAAe,EAAE;UAAM,CAAC,CAAC,CAAC;QAC/D,CAAC,MAAM;UACL1B,aAAa,CAAC,IAAI,IAAIH,IAAI,CAACpB,KAAK,IAAI,iCAAiC,CAAC,CAAC;QACzE;MACF,CAAC,MAAM;QACL;QACA,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnC,WAAW,YAAY,EAAE;UACvDoE,MAAM,EAAE,MAAM;UACdhC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAU/B,WAAW;UACtC,CAAC;UACD+D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACV,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMvB,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;UACfC,aAAa,CAACH,IAAI,CAACmC,MAAM,CAAC;UAE1B,IAAInC,IAAI,CAACoC,KAAK,IAAIpC,IAAI,CAACoC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;YACvChD,eAAe,CAACW,IAAI,CAACoC,KAAK,CAAC;YAC3BjC,aAAa,CAAC,oGAAoG,EAAE,IAAI,EAAE,eAAe,CAAC;YAC1IlB,mBAAmB,CAAC,IAAI,CAAC;YACzBE,eAAe,CAAC,eAAe,CAAC;UAClC,CAAC,MAAM,IAAI,CAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ,EAAE;YACjDtB,aAAa,CAAC,oDAAoD,EAAE,IAAI,EAAE,cAAc,CAAC;YACzFlB,mBAAmB,CAAC,IAAI,CAAC;YACzBE,eAAe,CAAC,cAAc,CAAC;UACjC;QACF,CAAC,MAAM;UACLgB,aAAa,CAAC,IAAI,IAAIH,IAAI,CAACpB,KAAK,IAAI,yBAAyB,CAAC,CAAC;QACjE;MACF;IACF,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,aAAa,EAAE0B,GAAG,CAAC;MACjCH,aAAa,CAAC,oCAAoC,CAAC;IACrD,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6D,mBAAmB,GAAG,MAAAA,CAAO1C,QAAQ,EAAEmB,IAAI,KAAK;IACpD9B,mBAAmB,CAAC,KAAK,CAAC;IAC1BiC,cAAc,CAACtB,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC;IAEvC,IAAImB,IAAI,KAAK,eAAe,EAAE;MAC5B,IAAInB,QAAQ,EAAE;QACZ;QACA,IAAIR,YAAY,CAACiD,MAAM,GAAG,CAAC,EAAE;UAC3B,MAAME,IAAI,GAAGnD,YAAY,CAAC,CAAC,CAAC;UAC5B,MAAMoD,OAAO,GAAG,GAAG9E,WAAW,GAAG6E,IAAI,CAACE,GAAG,UAAUzE,WAAW,EAAE;UAChE0E,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;UAC9BrC,aAAa,CAAC,8EAA8E,EAAE,IAAI,EAAE,cAAc,CAAC;UACnHhB,eAAe,CAAC,cAAc,CAAC;UAC/BF,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF,CAAC,MAAM;QACL,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ,EAAE;UAC1CtB,aAAa,CAAC,oDAAoD,EAAE,IAAI,EAAE,cAAc,CAAC;UACzFhB,eAAe,CAAC,cAAc,CAAC;UAC/BF,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF;MACAI,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,MAAM,IAAI0B,IAAI,KAAK,cAAc,EAAE;MAClC,IAAInB,QAAQ,EAAE;QACZO,aAAa,CAAC,wCAAwC,CAAC;MACzD,CAAC,MAAM;QACL,IAAI,CAAArB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ,EAAE;UAC1CtB,aAAa,CAAC,sCAAsC,EAAE,IAAI,EAAE,cAAc,CAAC;UAC3EhB,eAAe,CAAC,cAAc,CAAC;UAC/BF,mBAAmB,CAAC,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLkB,aAAa,CAAC,2DAA2D,CAAC;UAC1EE,UAAU,CAAC,MAAMuC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;QACxC;MACF;IACF,CAAC,MAAM,IAAI7B,IAAI,KAAK,cAAc,EAAE;MAClC,IAAInB,QAAQ,EAAE;QACZ,MAAMiD,WAAW,CAAC,CAAC;MACrB,CAAC,MAAM;QACL1C,aAAa,CAAC,6EAA6E,CAAC;MAC9F;IACF;EACF,CAAC;EAED,MAAM0C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMjD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnC,WAAW,4BAA4B,EAAE;QACvEoE,MAAM,EAAE,MAAM;QACdhC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAU/B,WAAW;QACtC,CAAC;QACD+D,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBL,aAAa,EAAE9C,WAAW,CAAC6C,MAAM,CAACC,aAAa;UAC/CkB,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAIlD,QAAQ,CAACM,EAAE,EAAE;QACfC,aAAa,CAAC,gFAAgF,CAAC;QAC/FE,UAAU,CAAC,MAAMuC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLzC,aAAa,CAAC,6CAA6C,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,EAAE0B,GAAG,CAAC;MAC3CH,aAAa,CAAC,uCAAuC,CAAC;IACxD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAGA,CAAA,KAAM;IACzB3E,YAAY,CAAC8E,UAAU,CAAC,QAAQ,CAAC;IACjC9E,YAAY,CAAC8E,UAAU,CAAC,SAAS,CAAC;IAClC9E,YAAY,CAAC8E,UAAU,CAAC,UAAU,CAAC;IACnChF,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAMiF,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBjC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,IAAIzC,cAAc,EAAE;IAClB,oBACEjB,OAAA;MAAK4F,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnD/F,OAAA;QAAA+F,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCnG,OAAA;QAAA+F,QAAA,EAAG;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAEV;EAEA,IAAIhF,KAAK,EAAE;IACT,oBACEnB,OAAA;MAAK4F,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnD/F,OAAA;QAAI4F,KAAK,EAAE;UAAEQ,KAAK,EAAE;QAAU,CAAE;QAAAL,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CnG,OAAA;QAAA+F,QAAA,EAAI5E;MAAK;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdnG,OAAA;QAAQqG,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAAC,UAAU,CAAE;QAACsF,KAAK,EAAE;UAAEU,SAAS,EAAE,MAAM;UAAET,OAAO,EAAE;QAAY,CAAE;QAAAE,QAAA,EAAC;MAEjG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEnG,OAAA;IAAK4F,KAAK,EAAE;MACVW,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,MAAM,EAAE,OAAO;MACfC,UAAU,EAAE;IACd,CAAE;IAAAX,QAAA,gBAEA/F,OAAA;MAAK4F,KAAK,EAAE;QACVC,OAAO,EAAE,WAAW;QACpBc,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBACA/F,OAAA;QAAI4F,KAAK,EAAE;UAAEmB,MAAM,EAAE,CAAC;UAAEX,KAAK,EAAE;QAAO,CAAE;QAAAL,QAAA,EACrC,CAAA1E,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2C,YAAY,MAAK,QAAQ,GACnC,WAAW3C,WAAW,CAAC6C,MAAM,CAACC,aAAa,EAAE,GAC7C;MAAsB;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACLnG,OAAA;QACEqG,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAAC,UAAU,CAAE;QACpCsF,KAAK,EAAE;UACLC,OAAO,EAAE,UAAU;UACnBc,eAAe,EAAE,SAAS;UAC1BP,KAAK,EAAE,OAAO;UACdY,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnG,OAAA;MAAK4F,KAAK,EAAE;QACVuB,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,MAAM;QACjBvB,OAAO,EAAE,MAAM;QACfc,eAAe,EAAE;MACnB,CAAE;MAAAZ,QAAA,GACCpF,QAAQ,CAAC0G,GAAG,CAAEC,OAAO,iBACpBtH,OAAA;QAAsB4F,KAAK,EAAE;UAAE2B,YAAY,EAAE;QAAO,CAAE;QAAAxB,QAAA,gBACpD/F,OAAA;UAAK4F,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfM,cAAc,EAAES,OAAO,CAAChE,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UACzD,CAAE;UAAAyC,QAAA,eACA/F,OAAA;YAAK4F,KAAK,EAAE;cACV4B,QAAQ,EAAE,KAAK;cACf3B,OAAO,EAAE,WAAW;cACpBoB,YAAY,EAAE,MAAM;cACpBN,eAAe,EAAEW,OAAO,CAAChE,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;cAChE8C,KAAK,EAAEkB,OAAO,CAAChE,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;cACjDmE,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,EACCuB,OAAO,CAACvE;UAAO;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELmB,OAAO,CAACtE,WAAW,IAAIsE,OAAO,CAACrE,UAAU,IAAI1B,gBAAgB,iBAC5DvB,OAAA;UAAK4F,KAAK,EAAE;YAAEU,SAAS,EAAE,MAAM;YAAER,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,GACpDuB,OAAO,CAACrE,UAAU,KAAK,eAAe,iBACrCjD,OAAA,CAACH,mBAAmB;YAClB6H,UAAU,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAE;YAC7D8C,MAAM,EAAEA,CAAA,KAAM9C,mBAAmB,CAAC,KAAK,EAAE,eAAe;UAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACF,EACA,CAACmB,OAAO,CAACrE,UAAU,KAAK,cAAc,IAAIqE,OAAO,CAACrE,UAAU,KAAK,cAAc,kBAC9EjD,OAAA,CAACJ,YAAY;YACXgI,KAAK,EAAEA,CAAA,KAAM/C,mBAAmB,CAAC,IAAI,EAAEyC,OAAO,CAACrE,UAAU,CAAE;YAC3D4E,IAAI,EAAEA,CAAA,KAAMhD,mBAAmB,CAAC,KAAK,EAAEyC,OAAO,CAACrE,UAAU;UAAE;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GAhCOmB,OAAO,CAACnE,EAAE;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiCf,CACN,CAAC,EAEDpF,OAAO,iBACNf,OAAA;QAAK4F,KAAK,EAAE;UAAEE,SAAS,EAAE,QAAQ;UAAEM,KAAK,EAAE;QAAO,CAAE;QAAAL,QAAA,eACjD/F,OAAA;UAAA+F,QAAA,EAAG;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN,eAEDnG,OAAA;QAAK8H,GAAG,EAAEpH;MAAe;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNnG,OAAA;MAAK4F,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfc,eAAe,EAAE,OAAO;QACxBoB,SAAS,EAAE;MACb,CAAE;MAAAhC,QAAA,eACA/F,OAAA;QAAK4F,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEyB,GAAG,EAAE;QAAO,CAAE;QAAAjC,QAAA,gBAC3C/F,OAAA;UACEiI,KAAK,EAAEpH,KAAM;UACbqH,QAAQ,EAAG1C,CAAC,IAAK1E,QAAQ,CAAC0E,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;UAC1CG,UAAU,EAAE7C,cAAe;UAC3B8C,WAAW,EAAE9G,gBAAgB,GAAG,4CAA4C,GAAG,sBAAuB;UACtG+G,QAAQ,EAAEvH,OAAO,IAAIQ,gBAAiB;UACtCqE,KAAK,EAAE;YACLuB,IAAI,EAAE,CAAC;YACPtB,OAAO,EAAE,MAAM;YACfmB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBsB,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;UACZ;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFnG,OAAA;UACEqG,OAAO,EAAE3C,iBAAkB;UAC3B4E,QAAQ,EAAEvH,OAAO,IAAIQ,gBAAgB,IAAI,CAACV,KAAK,CAAC8C,IAAI,CAAC,CAAE;UACvDiC,KAAK,EAAE;YACLC,OAAO,EAAE,WAAW;YACpBc,eAAe,EAAE5F,OAAO,IAAIQ,gBAAgB,IAAI,CAACV,KAAK,CAAC8C,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,SAAS;YAClFyC,KAAK,EAAE,OAAO;YACdY,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAEnG,OAAO,IAAIQ,gBAAgB,IAAI,CAACV,KAAK,CAAC8C,IAAI,CAAC,CAAC,GAAG,aAAa,GAAG,SAAS;YAChF8E,QAAQ,EAAE;UACZ,CAAE;UAAA1C,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/F,EAAA,CA5ZuBF,iBAAiB;EAAA,QAClBR,SAAS,EACbC,WAAW;AAAA;AAAA+I,EAAA,GAFNxI,iBAAiB;AAAA,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}